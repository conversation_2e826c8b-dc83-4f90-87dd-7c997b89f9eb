// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::{
    fmt::Debug,
    sync::{Arc, RwLock},
};

use anyhow::{anyhow, bail, Result};
use bytes::{Buf, Bytes};
use hexhex::hex;
use kvenginepb::fts as ftspb;
use protobuf::Message;
use tikv_util::codec::number::NumberEncoder;
use xorf::{BinaryFuse8, Filter};

use crate::{
    codecutil::BytesExt,
    ia::ia_file::IaFile,
    table::{
        file::{File, TtlCache},
        fts::{HandleBlockCache, HandleBlockCacheKey},
        ChecksumType,
    },
};

mod builder;
mod mmap_directory;
#[cfg(test)]
mod test;

pub use builder::*;
pub use mmap_directory::*;

/// Magic number for FTS DedicatedFile format
pub const FTS_DEDICATED_FILE_MAGIC: u32 = 0xFEE74B2F;

/// Format version for FTS DedicatedFile
pub const FTS_DEDICATED_FILE_FORMAT_V1: u8 = 0x1;

/// Size of serialized footer in bytes
pub const FTS_DEDICATED_FILE_FOOTER_SIZE: usize = 52;

// TODO List:
// 1. Modify the parameters of the search function to avoid word segmentation
//    for each query
// 2. Currently, DedicatedFile mmaps the tantivy index data in .xxx files, which
//    ultimately results in the entire index data being downloaded. This
//    requires more refined handling of on-demand download requirements.
// 3. Add integrity check for DedicatedFile's data block.

#[derive(Debug, Clone, Copy, PartialEq)]
pub struct ScoredResult {
    pub doc_id: u32,
    pub score: f32,
}

#[derive(Debug)]
pub struct BitmapFilter<'a> {
    pub match_all: bool,
    pub match_partial: &'a [u8],
}

impl<'a> BitmapFilter<'a> {
    pub fn all_match() -> Self {
        Self {
            match_all: true,
            match_partial: &[],
        }
    }
}

/// Footer has a fixed size and is always located at the end of the file.
/// The footer size cannot be changed.
/// Format:
/// - 1 byte: format version
/// - 1 byte: checksum type
/// - 6 bytes: reserved
/// - 4 bytes: checksum of footer
/// - 4 bytes: checksum of remaining meta blocks
/// - 8 bytes: data block offset (Tantivy data)
/// - 8 bytes: pk filter block offset
/// - 8 bytes: handle index block offset
/// - 8 bytes: property block offset
/// - 4 bytes: magic number
#[repr(C)]
#[derive(Default, Clone, Copy, Debug)]
pub struct DedicatedFileFooter {
    pub format: u8,
    pub checksum_type: ChecksumType,
    pub checksum_other_meta: u32,
    pub data_block_offset: u64,
    pub pk_filter_block_offset: u64,
    pub handle_index_block_offset: u64,
    pub prop_offset: u64,
    pub magic: u32,
}

impl DedicatedFileFooter {
    /// Get the size of the footer
    pub const fn footer_size() -> usize {
        FTS_DEDICATED_FILE_FOOTER_SIZE
    }

    /// Create a new footer with default values
    pub fn new() -> Self {
        Self {
            format: FTS_DEDICATED_FILE_FORMAT_V1,
            checksum_type: ChecksumType::None,
            checksum_other_meta: 0,
            data_block_offset: 0,
            pk_filter_block_offset: 0,
            handle_index_block_offset: 0,
            prop_offset: 0,
            magic: FTS_DEDICATED_FILE_MAGIC,
        }
    }

    /// Marshal footer to bytes
    pub fn marshal<W: std::io::Write>(&self, mut w: W) -> Result<usize> {
        use bytes::BufMut;

        let mut footer = [0u8; FTS_DEDICATED_FILE_FOOTER_SIZE];
        let mut data = &mut footer[..];

        // Write fields according to the format specification
        data.put_u8(self.format); // 1 byte: format version
        data.put_u8(self.checksum_type.value()); // 1 byte: checksum type
        data.put_slice(&[0; 6]); // 6 bytes: reserved
        data.put_slice(&[0; 4]); // 4 bytes: placeholder for checksum of footer
        data.put_u32_le(self.checksum_other_meta); // 4 bytes: checksum of remaining meta blocks
        data.put_u64_le(self.data_block_offset); // 8 bytes: data block offset (Tantivy data)
        data.put_u64_le(self.pk_filter_block_offset); // 8 bytes: pk filter block offset
        data.put_u64_le(self.handle_index_block_offset); // 8 bytes: handle index block offset
        data.put_u64_le(self.prop_offset); // 8 bytes: property block offset
        data.put_u32_le(self.magic); // 4 bytes: magic number

        // Calculate and write the footer checksum
        let checksum_footer = self.checksum_type.checksum(&footer);
        footer[8..12].copy_from_slice(&checksum_footer.to_le_bytes());

        w.write_all(&footer)?;

        Ok(FTS_DEDICATED_FILE_FOOTER_SIZE)
    }

    /// Unmarshal footer from bytes
    pub fn unmarshal(mut data: &[u8]) -> Result<Self> {
        if data.len() != FTS_DEDICATED_FILE_FOOTER_SIZE {
            bail!(
                "Invalid footer size, expected {:#x}, got {:#x}",
                FTS_DEDICATED_FILE_FOOTER_SIZE,
                data.len()
            );
        }
        let mut copied_data = [0u8; FTS_DEDICATED_FILE_FOOTER_SIZE];
        copied_data.copy_from_slice(data);

        // Read fields according to the format specification
        let format = data.get_u8(); // 1 byte: format version
        let checksum_type_value = data.get_u8(); // 1 byte: checksum type
        let checksum_type = ChecksumType::from(checksum_type_value);
        data.advance(6); // 6 bytes: reserved
        let checksum_footer = data.get_u32_le(); // 4 bytes: checksum of footer
        let checksum_other_meta = data.get_u32_le(); // 4 bytes: checksum of remaining meta blocks
        let data_block_offset = data.get_u64_le(); // 8 bytes: data block offset (Tantivy data)
        let pk_filter_block_offset = data.get_u64_le(); // 8 bytes: pk filter block offset
        let handle_index_block_offset = data.get_u64_le(); // 8 bytes: handle index block offset
        let prop_offset = data.get_u64_le(); // 8 bytes: property block offset
        let magic = data.get_u32_le(); // 4 bytes: magic number

        // Validate magic number
        if magic != FTS_DEDICATED_FILE_MAGIC {
            bail!(
                "Invalid magic number, expected {:#x}, got {:#x}",
                FTS_DEDICATED_FILE_MAGIC,
                magic
            );
        }

        // Validate format version
        if format != FTS_DEDICATED_FILE_FORMAT_V1 {
            bail!(
                "Unsupported format version, expected {:#x}, got {:#x}",
                FTS_DEDICATED_FILE_FORMAT_V1,
                format
            );
        }

        {
            // Verify footer's checksum
            copied_data[8..12].fill(0);
            let checksum_footer_actual = checksum_type.checksum(&copied_data);
            if checksum_footer != checksum_footer_actual {
                copied_data[8..12].copy_from_slice(&checksum_footer.to_le_bytes());
                bail!(
                    "FtsDedicatedFile footer checksum mismatch, actual {:#08x}, expect {:#08x}, footer data: {}, ChecksumType={:?}",
                    checksum_footer_actual,
                    checksum_footer,
                    hex(&copied_data),
                    checksum_type,
                );
            }
        }

        Ok(Self {
            format,
            checksum_type,
            checksum_other_meta,
            data_block_offset,
            pk_filter_block_offset,
            handle_index_block_offset,
            prop_offset,
            magic,
        })
    }
}

/// An in-memory FtsDedicatedFile. Only minimal metadata is kept in memory.
/// Main data is accessed through mmap, accelerated data is cached through
/// TtlCache.
#[derive(Clone)]
pub struct DedicatedFile<Info>(Arc<DedicatedFileCore<Info>>);

/// Core implementation of DedicatedFile
struct DedicatedFileCore<Info> {
    file: Arc<IaFile>,

    handle_index_block: TtlCache<ftspb::DedicatedFileHandleIndexBlock>,
    pk_filter: TtlCache<BinaryFuse8>,
    props: ftspb::DedicatedFilePropertyBlock,
    footer: DedicatedFileFooter,
    handle_block_checked: RwLock<Vec<bool>>, // AtomicBool would be better

    handle_block_accessor_cache: HandleBlockCache,

    // Tantivy index cache - Caches initialized tantivy Index.
    // tantivy_index_cache: OnceCell<tantivy::Index>,

    additional_info: Info,
}

impl DedicatedFile<()> {
    /// Create a new PackedFile from a remote file.
    pub fn new(file: Arc<IaFile>, handle_cache: HandleBlockCache) -> Result<DedicatedFile<()>> {
        DedicatedFile::<()>::new_with_info(file, handle_cache, ())
    }

    /// Calculate IA segment offsets for an FTS dedicated file based on handle
    /// blocks and data block. This function reads the handle index block and
    /// calculates segment boundaries based on the standard IA segment size.
    ///
    /// DedicatedFile layout: Handle Blocks -> Data Block -> Metadata Blocks ->
    /// Footer We need to create segments that respect both handle block
    /// boundaries and the large data block.
    pub fn generate_ia_segment_boundaries(file: &IaFile, segment_size: u64) -> Result<Vec<u64>> {
        let footer = Self::load_footer(file)?;
        let handle_index_block = Self::load_handle_index_block(file, &footer)?;

        let mut segment_offsets = vec![0]; // Always start with offset 0
        let mut current_segment_start = 0u64;

        // 1. First, create segments based on handle block boundaries
        // Handle blocks are typically small and numerous, so we group them into
        // segments
        for &block_offset in &handle_index_block.handle_block_offsets {
            // If this handle block would make the current segment exceed the segment size,
            // start a new segment
            if block_offset >= current_segment_start + segment_size {
                segment_offsets.push(block_offset);
                current_segment_start = block_offset;
            }
        }

        // 2. Handle the data block (Tantivy data) which is typically very large
        // The data block starts after all handle blocks
        let data_block_start = footer.data_block_offset;
        if data_block_start >= current_segment_start + segment_size {
            segment_offsets.push(data_block_start);
            current_segment_start = data_block_start;
        }

        // 3. Create segment boundaries based on individual Tantivy files within the
        //    data_block
        // Load props to get Tantivy file information
        let props = Self::load_props(file, &footer)?;
        let tantivy_files = props.get_tantivy_files();

        // Collect boundaries of all Tantivy files
        let mut tantivy_file_boundaries = Vec::new();
        let file_ranges = [
            tantivy_files.get_meta(),
            tantivy_files.get_managed(),
            tantivy_files.get_term(),
            tantivy_files.get_idx(),
            tantivy_files.get_pos(),
            tantivy_files.get_store(),
            tantivy_files.get_fast(),
            tantivy_files.get_fieldnorm(),
        ];

        for file_range in file_ranges {
            if file_range.get_size() > 0 {
                let file_start = data_block_start + file_range.get_offset();
                tantivy_file_boundaries.push(file_start);
            }
        }

        // Sort by file start position and remove duplicates.
        tantivy_file_boundaries.sort_unstable();
        tantivy_file_boundaries.dedup();

        // Create segment boundaries for each Tantivy file.
        for &file_start in &tantivy_file_boundaries {
            // If the file start is far enough from the current segment start, create a new
            // segment.
            if file_start > current_segment_start
                && (file_start >= current_segment_start + segment_size
                    || segment_offsets.last() != Some(&file_start))
            {
                segment_offsets.push(file_start);
                current_segment_start = file_start;
            }
        }

        segment_offsets.push(footer.handle_index_block_offset);

        Ok(segment_offsets)
    }

    /// Load the footer from the packed file.
    fn load_footer(file: &IaFile) -> Result<DedicatedFileFooter> {
        let footer_data = file.read_footer(FTS_DEDICATED_FILE_FOOTER_SIZE)?;
        let footer = DedicatedFileFooter::unmarshal(footer_data.as_ref())?;
        Self::verify_meta_checksum(file, &footer)?;
        Ok(footer)
    }

    /// Load the properties block from the dedicated file.
    /// Property block is the last metadata block, located between prop_offset
    /// and footer. According to DedicatedFileBuilder write order: Handle
    /// Index -> PK Filter -> Property -> Footer
    fn load_props(
        file: &IaFile,
        footer: &DedicatedFileFooter,
    ) -> Result<ftspb::DedicatedFilePropertyBlock> {
        let prop_offset = footer.prop_offset;
        let file_size = file.size();
        let footer_start = file_size - FTS_DEDICATED_FILE_FOOTER_SIZE as u64;

        // Verify that Property Block offset is valid
        if prop_offset >= footer_start {
            bail!(
                "Invalid property block offset {:#x}, footer starts at {:#x}",
                prop_offset,
                footer_start
            );
        }

        // Property Block is located between prop_offset and footer start
        let prop_len = (footer_start - prop_offset) as usize;
        if prop_len == 0 {
            bail!("Property block has zero size, this should not happen");
        }

        let data = file.read_table_meta(prop_offset, prop_len)?;
        let mut prop = ftspb::DedicatedFilePropertyBlock::new();
        prop.merge_from_bytes(&data)
            .map_err(|e| anyhow!("Failed to parse property block: {}", e))?;

        // Verify that required fields are present
        if prop.get_lp_key().is_empty() {
            bail!("Property block is missing lp_key field");
        }

        Ok(prop)
    }

    /// Verify the metadata checksum for the dedicated file.
    /// The checksum covers metadata blocks in order: handle index, pk filter,
    /// and property blocks. This matches the order used in
    /// DedicatedFileBuilder::finish().
    fn verify_meta_checksum(file: &IaFile, footer: &DedicatedFileFooter) -> Result<()> {
        let mut meta_checksum = 0u32;

        let file_size = file.size();
        let footer_start = file_size - FTS_DEDICATED_FILE_FOOTER_SIZE as u64;
        let meta_start = footer.handle_index_block_offset;
        let meta_len = footer_start - meta_start;
        let meta_data = file.read_table_meta(meta_start, meta_len as usize)?;
        meta_checksum = footer.checksum_type.append(meta_checksum, &meta_data);

        // Verify accumulated checksum
        if meta_checksum != footer.checksum_other_meta {
            bail!(
                "FtsDedicatedFile meta checksum mismatch, actual {:#08x}, expect {:#08x}, ChecksumType={:?}",
                meta_checksum,
                footer.checksum_other_meta,
                footer.checksum_type
            );
        }
        Ok(())
    }

    /// Load the handle index block from the dedicated file.
    fn load_handle_index_block(
        file: &IaFile,
        footer: &DedicatedFileFooter,
    ) -> Result<ftspb::DedicatedFileHandleIndexBlock> {
        let offset = footer.handle_index_block_offset;
        let length = footer.pk_filter_block_offset - footer.handle_index_block_offset;
        let data = file.read_table_meta(offset, length as usize)?;

        let mut handle_index_block = ftspb::DedicatedFileHandleIndexBlock::new();
        handle_index_block.merge_from_bytes(&data)?;

        if handle_index_block.handle_block_start_key.is_empty()
            || handle_index_block.handle_block_offsets.is_empty()
        {
            bail!(
                "handle_index_block broken: handle_block_start_key and handle_block_offsets must not be empty"
            );
        }
        if handle_index_block.handle_block_start_key.len() + 1
            != handle_index_block.handle_block_offsets.len()
        {
            bail!(
                "handle_index_block broken: handle_block_start_key.len={} but handle_block_offsets.len={}, expected N+1 relationship",
                handle_index_block.handle_block_start_key.len(),
                handle_index_block.handle_block_offsets.len()
            );
        }

        Ok(handle_index_block)
    }

    /// Load the PK filter block from the dedicated file.
    fn load_pk_filter(file: &IaFile, footer: &DedicatedFileFooter) -> Result<BinaryFuse8> {
        let offset = footer.pk_filter_block_offset;
        let length = footer.prop_offset - footer.pk_filter_block_offset;
        let data = file.read_table_meta(offset, length as usize)?;

        let filter =
            BinaryFuse8::try_from_bytes(&data).map_err(|e| anyhow!("Bad pk filter: {}", e))?;
        Ok(filter)
    }
}

impl<Info: Debug> Debug for DedicatedFile<Info> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("DedicateddFile")
            .field("file", &self.0.file)
            .field("props", &self.0.props)
            .field("info", &self.0.additional_info)
            .finish()
    }
}

impl<Info> DedicatedFile<Info> {
    // ... new, load_footer, load_props, load_pk_filter and other methods ...
    // [These method implementations are very similar to PackedFile, just with
    // different offsets and metadata structures]

    pub fn new_with_info(
        file: Arc<IaFile>,
        handle_cache: HandleBlockCache,
        additional_info: Info,
    ) -> Result<DedicatedFile<Info>> {
        if file.is_sync() {
            // Currently local file is not supported.
            bail!(
                "FtsDedicatedFile can be only accessed via IaFile, got {:?}",
                file.path()
            );
        }

        let footer = DedicatedFile::load_footer(file.as_ref())?;
        let props = DedicatedFile::load_props(file.as_ref(), &footer)?;
        Ok(Self(Arc::new(DedicatedFileCore::<_> {
            file,
            handle_index_block: TtlCache::default(),
            pk_filter: TtlCache::default(),
            props,
            footer,
            handle_block_checked: RwLock::new(vec![]),
            handle_block_accessor_cache: handle_cache,
            additional_info,
        })))
    }

    /// Get or load Handle index block.
    fn get_handle_index_block(&self) -> Result<Arc<ftspb::DedicatedFileHandleIndexBlock>> {
        // ... TtlCache get-or-create implementation ...
        self.0
            .handle_index_block
            .get(|| DedicatedFile::load_handle_index_block(&self.0.file, &self.0.footer))
    }

    /// Get or load primary key filter.
    fn get_pk_filter(&self) -> Result<Arc<BinaryFuse8>> {
        // ... TtlCache get-or-create implementation ...
        self.0
            .pk_filter
            .get(|| DedicatedFile::load_pk_filter(&self.0.file, &self.0.footer))
    }

    /// Get footer.
    pub fn footer(&self) -> &DedicatedFileFooter {
        &self.0.footer
    }

    /// Get property block.
    pub fn props(&self) -> &ftspb::DedicatedFilePropertyBlock {
        &self.0.props
    }

    /// Check if this file stores the specified integer PK with version >
    /// given_version and <= max_version. This is used for MVCC checks.
    pub async fn has_newer_version_int(
        &self,
        pk: i64,
        version: u64,
        max_version: u64,
    ) -> Result<bool> {
        // 1. First check file-level PK filter
        let pk_filter = self.get_pk_filter()?;
        if !pk_filter.contains(&(pk as u64)) {
            return Ok(false);
        }

        // 2. Find Handle Block that might contain this PK in Handle index block
        let handle_index = self.get_handle_index_block()?;
        let mut key = Vec::with_capacity(64);
        key.encode_i64(pk).unwrap();
        let block_idx = match self.find_handle_block_index(key.as_slice())? {
            Some(idx) => idx,
            None => return Ok(false),
        };

        // 3. Get Handle Block data and verify
        let block_accessor = self
            .get_handle_block_accessor(block_idx, &handle_index, true)
            .await?;

        block_accessor.has_newer_version_int(pk, version, max_version)
    }

    /// Check if this file stores the specified common PK with version >
    /// given_version and <= max_version. This is used for MVCC checks.
    pub async fn has_newer_version_common(
        &self,
        pk: &[u8],
        version: u64,
        max_version: u64,
    ) -> Result<bool> {
        // 1. First check file-level PK filter
        let pk_filter = self.get_pk_filter()?;
        let pk_hash = farmhash::fingerprint64(pk);
        if !pk_filter.contains(&pk_hash) {
            return Ok(false);
        }

        // 2. Find Handle Block that might contain this PK in Handle index block
        let handle_index = self.get_handle_index_block()?;
        let block_idx = match self.find_handle_block_index(pk)? {
            Some(idx) => idx,
            None => return Ok(false),
        };

        // 3. Get Handle Block data and verify
        let block_accessor = self
            .get_handle_block_accessor(block_idx, &handle_index, false)
            .await?;

        block_accessor.has_newer_version_common(pk, version, max_version)
    }

    async fn get_handle_block_accessor(
        &self,
        block_idx: usize,
        handle_index: &ftspb::DedicatedFileHandleIndexBlock,
        is_int_handle: bool,
    ) -> Result<Arc<HandleBlockAccessor>> {
        let cache_key = HandleBlockCacheKey::new(self.0.file.id(), block_idx);
        let init = self.load_handle_block_accessor(block_idx, handle_index, is_int_handle);

        self.0
            .handle_block_accessor_cache
            .try_get_with_async(cache_key, async { init.await.map(Arc::new) })
            .await
    }

    /// Load handle block from disk with checksum verification
    async fn load_handle_block_accessor(
        &self,
        block_idx: usize,
        handle_index: &ftspb::DedicatedFileHandleIndexBlock,
        is_int_handle: bool,
    ) -> Result<HandleBlockAccessor> {
        // Get Handle Block data
        if block_idx + 1 >= handle_index.handle_block_offsets.len() {
            bail!("Handle block index {} out of bounds", block_idx);
        }
        let block_offset = handle_index.handle_block_offsets[block_idx];

        // Calculate block end offset using N+1 offset array
        let block_end_offset = handle_index.handle_block_offsets[block_idx + 1];
        let block_len = block_end_offset - block_offset;

        let (data, _) = self
            .0
            .file
            .mmap_range(block_offset, block_len as usize)
            .await?;

        // Use HandleBlockAccessor to search within the block
        let block_accessor = HandleBlockAccessor::new(data, is_int_handle)?;

        // Verify checksum if not already verified
        let need_checksum = {
            let handle_block_checked = self.0.handle_block_checked.read().unwrap();
            handle_block_checked.is_empty()
                || !handle_block_checked.get(block_idx).unwrap_or(&false)
        };

        if need_checksum {
            let checksum_type = ChecksumType::from(self.0.footer.checksum_type as u8);
            block_accessor.verify_checksum(checksum_type)?;

            // Mark this handle block as verified
            {
                let mut handle_block_checked = self.0.handle_block_checked.write().unwrap();
                if handle_block_checked.is_empty() {
                    handle_block_checked.resize(handle_index.handle_block_offsets.len(), false);
                }
                if block_idx < handle_block_checked.len() {
                    handle_block_checked[block_idx] = true;
                }
            }
        }

        Ok(block_accessor)
    }

    /// Find the index of Handle Block that might contain the given primary key.
    fn find_handle_block_index(&self, pk: &[u8]) -> Result<Option<usize>> {
        let index_block = self.get_handle_index_block()?;
        let n = index_block.handle_block_start_key.len();

        let pos =
            crate::table::search(n, |i| index_block.handle_block_start_key[i].as_slice() > pk);

        if pos == 0 {
            return Ok(None);
        }

        Ok(Some(pos - 1))
    }
}

/// A zero-copy accessor for reading Handle Block.
#[derive(Clone)]
pub struct HandleBlockAccessor {
    data: Bytes,
    n_pk: u32,
    // Following fields are slices of data, do not own the data
    versions: &'static [u64],
    #[allow(unused)]
    delete_marks: &'static [u8],
    pk_common_offsets: &'static [u64],
    pk_data: Bytes,
    pks_int: &'static [i64],
}

impl HandleBlockAccessor {
    /// Parse Handle Block binary layout
    /// Handle Block structure:
    /// u32    Number of Primary Keys
    /// ...    Align to 8 bytes
    /// [u64]  Versions (mmap mapped)
    /// [u8]   Delete Mark (mmap mapped)
    /// ...    Align to 8 bytes
    /// [u32]  Common Handle Offsets (only exists for Common Handle, mmap
    /// mapped, has n+1 items) ...    Align to 8 bytes
    /// ...    Primary Key Data Bytes (mmap mapped)
    /// u32    Checksum (checksum of all above fields)
    /// ...    Align to 8 bytes
    fn new(mut data: Bytes, is_pk_int: bool) -> Result<Self> {
        use crate::codecutil::next_aligned_offset;

        let original_data = data.clone();

        // 1. Read PK count (u32)
        let n_pk = data.get_u32_le();
        if n_pk == 0 {
            bail!("Handle block cannot have zero PKs");
        }

        // 2. Skip padding to align to 8 bytes
        let mut current_offset = 4; // u32 for n_pk
        let next_offset = next_aligned_offset(current_offset, 8);
        if next_offset > current_offset {
            data.advance(next_offset - current_offset);
            current_offset = next_offset;
        }

        // 3. Read versions array [u64] (mmap mapped)
        let versions_size = (n_pk as usize) * 8;
        let versions_data = data.try_get_first(versions_size)?;
        let versions = versions_data.try_as_slice::<u64>()?;
        current_offset += versions_size;

        // 4. Read delete marks array [u8] (mmap mapped)
        let delete_marks_size = n_pk as usize;
        let delete_marks_data = data.try_get_first(delete_marks_size)?;
        let delete_marks = delete_marks_data.as_ref();
        current_offset += delete_marks_size;

        // 5. Align to 8 bytes
        let next_offset = next_aligned_offset(current_offset, 8);
        if next_offset > current_offset {
            data.advance(next_offset - current_offset);
            current_offset = next_offset;
        }

        // 6. Handle Common Handle Offsets [u64] (only exists for Common Handle, mmap
        //    mapped, has n+1 items)
        let (pk_common_offsets, pk_data_size) = if !is_pk_int {
            let offsets_size = ((n_pk + 1) as usize) * 8; // n_pk + 1 u64s
            let offsets_data = data.try_get_first(offsets_size)?;
            let offsets = offsets_data.try_as_slice::<u64>()?;
            current_offset += offsets_size;

            // 7. Align to 8 bytes
            let next_offset = next_aligned_offset(current_offset, 8);
            if next_offset > current_offset {
                data.advance(next_offset - current_offset);
                current_offset = next_offset;
                let _ = current_offset;
            }

            let pk_data_size = offsets[n_pk as usize] as usize;

            // Use unsafe extend_lifetime to avoid lifetime issues
            let offsets_static = unsafe { extend_lifetime(offsets) };
            (offsets_static, pk_data_size)
        } else {
            // For integer PK, no offset array, directly calculate data size
            (&[] as &[u64], (n_pk as usize) * 8) // Integer PK: u64 * n_pk
        };

        // 8. Read Primary Key Data Bytes (mmap mapped)
        let pk_data = data.try_get_first(pk_data_size)?;

        let pks_int = if is_pk_int {
            let slice = pk_data.try_as_slice::<i64>()?;
            unsafe { extend_lifetime(slice) }
        } else {
            &[]
        };

        // Use unsafe extend_lifetime to avoid lifetime issues
        // This is safe because versions and delete_marks have the same lifetime as
        // original_data
        let versions_static = unsafe { extend_lifetime(versions) };
        let delete_marks_static = unsafe { extend_lifetime(delete_marks) };

        Ok(Self {
            data: original_data,
            n_pk,
            versions: versions_static,
            delete_marks: delete_marks_static,
            pk_common_offsets,
            pk_data,
            pks_int,
        })
    }

    /// Verify the checksum of this Handle Block
    fn verify_checksum(&self, checksum_type: ChecksumType) -> Result<()> {
        let mut data_clone = self.data.clone();

        // The block is aligned to 8 bytes. The layout is:
        // [content][checksum: 4 bytes][padding: 4 bytes]
        // We read the last 8 bytes to get the checksum_bytes.
        let checksum_bytes = data_clone.try_get_last(8)?;

        // The checksum_bytes contains the checksum in its first 4 bytes.
        let mut checksum_reader = &checksum_bytes[..4];
        let checksum = checksum_reader.get_u32_le();

        let calculated_checksum = checksum_type.checksum(&data_clone);

        if checksum != calculated_checksum {
            bail!(
                "Handle block checksum mismatch: calculated {:#08x}, stored {:#08x}",
                calculated_checksum,
                checksum
            );
        }

        Ok(())
    }

    fn int_pk_at(&self, i: usize) -> Result<i64> {
        if i >= self.n_pk as usize {
            bail!("PK index {} out of bounds, n_pk={}", i, self.n_pk);
        }

        // Integer PK: directly read from the pre-casted slice
        Ok(self.pks_int[i])
    }

    fn common_pk_at(&self, i: usize) -> Result<&[u8]> {
        if i >= self.n_pk as usize {
            bail!("PK index {} out of bounds, n_pk={}", i, self.n_pk);
        }
        // Common PK: use offset array to extract data
        let start_offset = self.pk_common_offsets[i] as usize;
        let end_offset = self.pk_common_offsets[i + 1] as usize;

        if start_offset > end_offset || end_offset > self.pk_data.len() {
            bail!(
                "Invalid PK offsets: start={}, end={}, pk_data_len={}",
                start_offset,
                end_offset,
                self.pk_data.len()
            );
        }

        Ok(&self.pk_data[start_offset..end_offset])
    }

    fn version_at(&self, i: usize) -> Result<u64> {
        if i >= self.n_pk as usize {
            bail!("PK index {} out of bounds, n_pk={}", i, self.n_pk);
        }
        Ok(self.versions[i])
    }

    #[allow(unused)]
    fn delete_mark_at(&self, i: usize) -> Result<bool> {
        if i >= self.n_pk as usize {
            bail!("Version index {} out of bounds, n_pk={}", i, self.n_pk);
        }
        // In delete_marks array, 0 means not deleted, non-0 means deleted
        Ok(self.delete_marks[i] != 0)
    }

    /// Search for newer versions within this Handle Block for integer PK.
    fn has_newer_version_int(&self, pk: i64, version: u64, max_version: u64) -> Result<bool> {
        // For integer PK, can directly use helper function
        has_newer_version(
            pk,
            version,
            max_version,
            self.n_pk as usize,
            |i| self.version_at(i),
            |i| self.int_pk_at(i),
        )
    }

    /// Search for newer versions within this Handle Block for common PK.
    fn has_newer_version_common(&self, pk: &[u8], version: u64, max_version: u64) -> Result<bool> {
        has_newer_version(
            pk,
            version,
            max_version,
            self.n_pk as usize,
            |i| self.version_at(i),
            |i| self.common_pk_at(i),
        )
    }
}

/// Find whether there is a newer version of a specified PK.
/// This function is the same as the implementation in PackedFile, used for
/// binary search within Handle Block
fn has_newer_version<PK: Ord>(
    target_pk: PK,
    target_version: u64,
    target_max_version: u64,
    n: usize,
    version_at: impl Fn(usize) -> Result<u64>,
    pk_at: impl Fn(usize) -> Result<PK>,
) -> Result<bool> {
    use std::cmp::Ordering;

    // Find the first position `pos` such that:
    // `pk_at(pos) >= target_pk` and `version_at(pos) <= target_max_version`
    let pos = crate::table::try_search::<anyhow::Error>(n, |i| match pk_at(i)?.cmp(&target_pk) {
        Ordering::Less => Ok(false),
        Ordering::Greater => Ok(true),
        Ordering::Equal => Ok(version_at(i)? <= target_max_version),
    })?;

    if pos >= n {
        return Ok(false);
    }

    let pk = pk_at(pos)?;
    if pk == target_pk {
        let version = version_at(pos)?;
        // We already know version <= target_max_version (from search condition)
        // Only need to check version > target_version
        if version > target_version {
            return Ok(true);
        }
    }

    Ok(false)
}

/// Unsafe function to extend lifetime
/// This is the same as the implementation in PackedFile, used to handle
/// lifetime issues
unsafe fn extend_lifetime<'b, T: ?Sized>(r: &'b T) -> &'static T {
    std::mem::transmute::<&'b T, &'static T>(r)
}
