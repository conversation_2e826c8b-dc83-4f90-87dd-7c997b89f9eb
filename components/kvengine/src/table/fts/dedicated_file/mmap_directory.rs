// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::{
    io,
    ops::{Deref, Range},
    path::Path,
    sync::Arc,
};

use bytes::Bytes;
use tantivy::directory::{
    error::{DeleteError, LockError, OpenReadError, OpenWriteError},
    Directory, DirectoryLock, FileHandle, FileSlice, Lock, OwnedBytes, WatchCallback, WatchHandle,
    WritePtr,
};

/// BytesDirectory - A custom Tantivy Directory implementation.
///
/// This directory holds pre-sliced `Bytes` references into a single
/// mmap'd data block for each of Tantivy's fixed file types.
/// This is far more efficient than a HashMap for a small, fixed set of files.
#[derive(Debug, <PERSON>lone)]
pub struct BytesDirectory {
    // Direct fields for each file type. This is far more efficient
    // than a HashMap for a small, fixed set of files.
    pub meta: Bytes,
    pub managed: Bytes,
    pub term: Bytes,
    pub idx: Bytes,
    pub pos: Bytes,
    pub store: Bytes,
    pub fast: Bytes,
    pub fieldnorm: Bytes,
}

impl BytesDirectory {
    /// Gets the corresponding memory slice for a given file path.
    /// Supports Tantivy-generated filenames with hash prefixes.
    /// This method uses direct field access which is more efficient than
    /// HashMap lookups.
    fn get_file_bytes(&self, path: &Path) -> Option<&Bytes> {
        let file_name = path.file_name()?.to_str()?;

        let bytes = match file_name {
            s if s.ends_with(".term") => &self.term,
            s if s.ends_with(".idx") => &self.idx,
            s if s.ends_with(".pos") => &self.pos,
            s if s.ends_with(".store") => &self.store,
            s if s.ends_with(".fast") => &self.fast,
            s if s.ends_with(".fieldnorm") => &self.fieldnorm,
            "meta.json" => &self.meta,
            ".managed.json" => &self.managed,
            _ => return None,
        };

        if bytes.is_empty() { None } else { Some(bytes) }
    }
}

/// Wraps `bytes::Bytes` to implement `StableDeref`.
///
/// # Safety
///
/// This is safe because `bytes::Bytes` is an immutable, reference-counted byte
/// slice. The underlying data it points to is on the heap and is never moved.
/// Therefore, the pointer returned by `Deref` is stable for the lifetime of the
/// object.
#[derive(Clone)]
struct BytesStableDeref(Bytes);

impl Deref for BytesStableDeref {
    type Target = [u8];
    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

/// See the safety comment on the struct definition.
unsafe impl stable_deref_trait::StableDeref for BytesStableDeref {}

/// MmapFileHandle - A FileHandle implementation based on a memory slice.
#[derive(Debug)]
struct MmapFileHandle {
    data: Bytes,
}

impl std::ops::Deref for MmapFileHandle {
    type Target = [u8];

    fn deref(&self) -> &Self::Target {
        &self.data
    }
}

impl FileHandle for MmapFileHandle {
    fn read_bytes(&self, range: Range<usize>) -> Result<OwnedBytes, io::Error> {
        if range.end > self.data.len() {
            return Err(io::Error::new(
                io::ErrorKind::UnexpectedEof,
                "Read beyond file end",
            ));
        }

        // Slice the underlying `Bytes` object. `slice` is a cheap, zero-copy operation.
        let sliced_bytes = self.data.slice(range);

        // Wrap the sliced `Bytes` in our `BytesStableDeref` which implements
        // `StableDeref`.
        let holder = BytesStableDeref(sliced_bytes);

        // `OwnedBytes::new` can now accept our holder, creating the final object
        // without any data duplication.
        Ok(OwnedBytes::new(holder))
    }
}

/// Dummy lock implementation - no real lock needed as DedicatedFile is
/// immutable.
struct DummyLock;

impl Drop for DummyLock {
    fn drop(&mut self) {
        // Does nothing.
    }
}

impl Directory for BytesDirectory {
    fn get_file_handle(&self, path: &Path) -> Result<Arc<dyn FileHandle>, OpenReadError> {
        let data = self
            .get_file_bytes(path)
            .ok_or_else(|| OpenReadError::FileDoesNotExist(path.to_path_buf()))?
            .clone();
        Ok(Arc::new(MmapFileHandle {
            data,
        }))
    }

    fn open_read(&self, path: &Path) -> Result<FileSlice, OpenReadError> {
        let handle = self.get_file_handle(path)?;
        Ok(FileSlice::new(handle))
    }

    fn delete(&self, path: &Path) -> Result<(), DeleteError> {
        // DedicatedFile is read-only, delete is not supported.
        Err(DeleteError::FileDoesNotExist(path.to_path_buf()))
    }

    fn exists(&self, path: &Path) -> Result<bool, OpenReadError> {
        Ok(self.get_file_bytes(path).is_some())
    }

    fn open_write(&self, _path: &Path) -> Result<WritePtr, OpenWriteError> {
        // DedicatedFile is read-only, write is not supported.
        Err(OpenWriteError::FileAlreadyExists(_path.to_path_buf()))
    }

    fn atomic_read(&self, path: &Path) -> Result<Vec<u8>, OpenReadError> {
        let data = self
            .get_file_bytes(path)
            .ok_or_else(|| OpenReadError::FileDoesNotExist(path.to_path_buf()))?;
        Ok(data.to_vec())
    }

    fn atomic_write(&self, path: &Path, _data: &[u8]) -> io::Result<()> {
        // DedicatedFile is read-only, write is not supported.
        Err(io::Error::new(
            io::ErrorKind::PermissionDenied,
            format!(
                "Cannot write to read-only DedicatedFile: {}",
                path.display()
            ),
        ))
    }

    fn watch(&self, _watch_callback: WatchCallback) -> tantivy::Result<WatchHandle> {
        // DedicatedFile is immutable, no need to watch.
        Ok(WatchHandle::empty())
    }

    fn sync_directory(&self) -> io::Result<()> {
        // DedicatedFile is read-only, no need to sync.
        Ok(())
    }

    fn acquire_lock(&self, _lock: &Lock) -> Result<DirectoryLock, LockError> {
        // Returns a dummy lock because DedicatedFile is immutable.
        Ok(DirectoryLock::from(Box::new(DummyLock)))
    }
}

#[cfg(test)]
mod tests {
    use std::path::Path;

    use bytes::Bytes;
    use tantivy::{
        directory::{error::OpenReadError, Directory, WatchCallback},
        HasLen,
    };

    use super::*;

    /// Helper function to create test data for different file types
    fn create_test_data() -> BytesDirectory {
        BytesDirectory {
            meta: Bytes::from("meta.json content"),
            managed: Bytes::from(".managed.json content"),
            term: Bytes::from("segment.term content"),
            idx: Bytes::from("segment.idx content"),
            pos: Bytes::from("segment.pos content"),
            store: Bytes::from("segment.store content"),
            fast: Bytes::from("segment.fast content"),
            fieldnorm: Bytes::from("segment.fieldnorm content"),
        }
    }

    #[test]
    fn test_mmap_directory_creation() {
        let directory = create_test_data();

        // Verify that all expected files are present
        assert!(!directory.meta.is_empty());
        assert!(!directory.managed.is_empty());
        assert!(!directory.term.is_empty());
        assert!(!directory.idx.is_empty());
        assert!(!directory.pos.is_empty());
        assert!(!directory.store.is_empty());
        assert!(!directory.fast.is_empty());
        assert!(!directory.fieldnorm.is_empty());
    }

    #[test]
    fn test_mmap_directory_empty_files() {
        // Test with empty files
        let directory = BytesDirectory {
            meta: Bytes::new(),
            managed: Bytes::new(),
            term: Bytes::new(),
            idx: Bytes::new(),
            pos: Bytes::new(),
            store: Bytes::new(),
            fast: Bytes::new(),
            fieldnorm: Bytes::new(),
        };

        // All files should be absent (represented by empty Bytes)
        assert!(directory.meta.is_empty());
        assert!(directory.managed.is_empty());
        assert!(directory.term.is_empty());
        assert!(directory.idx.is_empty());
        assert!(directory.pos.is_empty());
        assert!(directory.store.is_empty());
        assert!(directory.fast.is_empty());
        assert!(directory.fieldnorm.is_empty());
    }

    #[test]
    fn test_mmap_directory_partial_files() {
        // Test with only some file types
        let directory = BytesDirectory {
            meta: Bytes::from("meta content"),
            managed: Bytes::new(),
            term: Bytes::new(),
            idx: Bytes::new(),
            pos: Bytes::new(),
            store: Bytes::new(),
            fast: Bytes::new(),
            fieldnorm: Bytes::new(),
        };

        // Only meta should be present
        assert!(!directory.meta.is_empty());
        assert!(directory.managed.is_empty());
        assert!(directory.term.is_empty());
        assert!(directory.idx.is_empty());
        assert!(directory.pos.is_empty());
        assert!(directory.store.is_empty());
        assert!(directory.fast.is_empty());
        assert!(directory.fieldnorm.is_empty());
    }

    #[test]
    fn test_exists_method() {
        let directory = create_test_data();

        // Test existing files
        assert!(directory.exists(Path::new("meta.json")).unwrap());
        assert!(directory.exists(Path::new(".managed.json")).unwrap());
        assert!(directory.exists(Path::new("segment.term")).unwrap());
        assert!(directory.exists(Path::new("segment.idx")).unwrap());
        assert!(directory.exists(Path::new("segment.pos")).unwrap());
        assert!(directory.exists(Path::new("segment.store")).unwrap());
        assert!(directory.exists(Path::new("segment.fast")).unwrap());
        assert!(directory.exists(Path::new("segment.fieldnorm")).unwrap());

        // Test non-existing files
        assert!(!directory.exists(Path::new("nonexistent.file")).unwrap());
        assert!(!directory.exists(Path::new("segment.unknown")).unwrap());
    }

    #[test]
    fn test_exists_with_hash_prefixes() {
        // Create test data with hash prefixes
        let directory = BytesDirectory {
            meta: Bytes::new(),
            managed: Bytes::new(),
            term: Bytes::from("term content"),
            idx: Bytes::from("idx content"),
            pos: Bytes::from("pos content"),
            store: Bytes::from("store content"),
            fast: Bytes::from("fast content"),
            fieldnorm: Bytes::from("fieldnorm content"),
        };

        // Test files with hash prefixes (Tantivy-generated filenames)
        assert!(directory.exists(Path::new("abc123.term")).unwrap());
        assert!(directory.exists(Path::new("def456.idx")).unwrap());
        assert!(directory.exists(Path::new("ghi789.pos")).unwrap());
        assert!(directory.exists(Path::new("jkl012.store")).unwrap());
        assert!(directory.exists(Path::new("mno345.fast")).unwrap());
        assert!(directory.exists(Path::new("pqr678.fieldnorm")).unwrap());
    }

    #[test]
    fn test_atomic_read() {
        let directory = create_test_data();

        // Test reading existing files
        let meta_content = directory.atomic_read(Path::new("meta.json")).unwrap();
        assert_eq!(meta_content, b"meta.json content");

        let managed_content = directory.atomic_read(Path::new(".managed.json")).unwrap();
        assert_eq!(managed_content, b".managed.json content");

        let term_content = directory.atomic_read(Path::new("segment.term")).unwrap();
        assert_eq!(term_content, b"segment.term content");

        // Test reading with hash prefixes
        let idx_content = directory.atomic_read(Path::new("abc123.idx")).unwrap();
        assert_eq!(idx_content, b"segment.idx content");
    }

    #[test]
    fn test_atomic_read_nonexistent_file() {
        let directory = create_test_data();

        // Test reading non-existent file
        let result = directory.atomic_read(Path::new("nonexistent.file"));
        assert!(matches!(result, Err(OpenReadError::FileDoesNotExist(_))));
    }

    #[test]
    fn test_get_file_handle() {
        let directory = create_test_data();

        // Test getting file handle for existing file
        let handle = directory.get_file_handle(Path::new("meta.json")).unwrap();
        assert_eq!(handle.len(), b"meta.json content".len());

        // Test getting file handle for non-existent file
        let result = directory.get_file_handle(Path::new("nonexistent.file"));
        assert!(matches!(result, Err(OpenReadError::FileDoesNotExist(_))));
    }

    #[test]
    fn test_open_read() {
        let directory = create_test_data();

        // Test opening existing file for reading
        let file_slice = directory.open_read(Path::new("segment.term")).unwrap();
        assert_eq!(file_slice.len(), b"segment.term content".len());

        // Test opening non-existent file
        let result = directory.open_read(Path::new("nonexistent.file"));
        assert!(matches!(result, Err(OpenReadError::FileDoesNotExist(_))));
    }

    #[test]
    fn test_read_only_operations() {
        let directory = create_test_data();

        // Test that write operations are not supported
        let write_result = directory.open_write(Path::new("test.file"));
        assert!(write_result.is_err());

        let atomic_write_result = directory.atomic_write(Path::new("test.file"), b"data");
        assert!(atomic_write_result.is_err());

        // Test that delete operations are not supported
        let delete_result = directory.delete(Path::new("meta.json"));
        assert!(delete_result.is_err());
    }

    #[test]
    fn test_file_handle_read_bytes() {
        let directory = create_test_data();

        let handle = directory
            .get_file_handle(Path::new("segment.term"))
            .unwrap();
        let content = b"segment.term content";

        // Test reading full content
        let full_read = handle.read_bytes(0..content.len()).unwrap();
        assert_eq!(full_read.as_slice(), content);

        // Test reading partial content
        let partial_read = handle.read_bytes(0..7).unwrap();
        assert_eq!(partial_read.as_slice(), b"segment");

        // Test reading beyond file end
        let beyond_read = handle.read_bytes(0..content.len() + 10);
        beyond_read.unwrap_err();
    }

    #[test]
    fn test_directory_sync_and_watch() {
        let directory = create_test_data();

        // Test sync_directory (should always succeed for read-only directory)
        directory.sync_directory().unwrap();

        // Test watch (should return empty handle for immutable directory)
        let watch_callback = WatchCallback::new(|| {});
        let watch_handle = directory.watch(watch_callback).unwrap();
        // The watch handle should be valid but empty
        drop(watch_handle);
    }

    // Note: acquire_lock test is skipped as it requires complex Lock construction
    // The functionality is tested indirectly through the Directory trait
    // implementation

    #[test]
    fn test_bounds_checking() {
        // Test with valid files only
        let directory = BytesDirectory {
            meta: Bytes::from("sma"),
            managed: Bytes::new(),
            term: Bytes::new(),
            idx: Bytes::new(),
            pos: Bytes::new(),
            store: Bytes::new(),
            fast: Bytes::new(),
            fieldnorm: Bytes::new(),
        };

        // meta.json should be present
        assert!(directory.exists(Path::new("meta.json")).unwrap());

        // segment.term should not be present (empty Bytes)
        assert!(!directory.exists(Path::new("segment.term")).unwrap());
    }

    #[test]
    fn test_zero_size_files() {
        // Test with empty and normal files
        let directory = BytesDirectory {
            meta: Bytes::new(),
            managed: Bytes::new(),
            term: Bytes::from("content"),
            idx: Bytes::new(),
            pos: Bytes::new(),
            store: Bytes::new(),
            fast: Bytes::new(),
            fieldnorm: Bytes::new(),
        };

        // A file with empty Bytes should not exist (get_file_bytes returns None)
        assert!(!directory.exists(Path::new("meta.json")).unwrap());

        // Normal file should be present
        assert!(directory.exists(Path::new("segment.term")).unwrap());
    }

    #[test]
    fn test_unknown_file_types() {
        // Test with known and unknown file types
        let directory = create_test_data();

        // Unknown file type should NOT be found by exists() (returns false)
        assert!(!directory.exists(Path::new("unknown.file")).unwrap());
    }

    #[test]
    fn test_clone_directory() {
        let directory = create_test_data();

        // Clone the directory
        let cloned_directory = directory.clone();

        // Both directories should have the same files
        assert!(directory.exists(Path::new("meta.json")).unwrap());
        assert!(cloned_directory.exists(Path::new("meta.json")).unwrap());

        // Both should be able to read the same content
        let original_content = directory.atomic_read(Path::new("meta.json")).unwrap();
        let cloned_content = cloned_directory
            .atomic_read(Path::new("meta.json"))
            .unwrap();
        assert_eq!(original_content, cloned_content);
    }

    #[test]
    fn test_invalid_path_handling() {
        let directory = create_test_data();

        // Test with invalid path (no filename)
        let result = directory.atomic_read(Path::new(""));
        assert!(matches!(result, Err(OpenReadError::FileDoesNotExist(_))));

        // Test with path that has no file extension
        let result = directory.atomic_read(Path::new("noextension"));
        assert!(matches!(result, Err(OpenReadError::FileDoesNotExist(_))));
    }
}
