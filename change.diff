diff --git a/components/cloud_server/src/tikv_server.rs b/components/cloud_server/src/tikv_server.rs
index bd784dfa8..1f7833381 100644
--- a/components/cloud_server/src/tikv_server.rs
+++ b/components/cloud_server/src/tikv_server.rs
@@ -1048,6 +1048,7 @@ impl TikvServer {
                 let size = (total_mem >> 30) as usize / 2;
                 size.clamp(2, 64)
             });
+        kv_opts.columnar_build_options = conf.kvengine.columnar_table_build_options;
 
         let opts = Arc::new(kv_opts);
         let id_allocator = Arc::new(PdIdAllocator::new(pd.clone()));
diff --git a/components/cloud_worker/src/schema_manager.rs b/components/cloud_worker/src/schema_manager.rs
index 7272038c3..0203f6269 100644
--- a/components/cloud_worker/src/schema_manager.rs
+++ b/components/cloud_worker/src/schema_manager.rs
@@ -1,4 +1,5 @@
 // Copyright 2024 TiKV Project Authors. Licensed under Apache-2.0.
+
 use std::{
     collections::HashMap,
     fs,
@@ -18,13 +19,10 @@ use kvengine::{
     dfs,
     dfs::Dfs,
     table::{
+        columnar,
         columnar::{
-            builder::{
-                new_common_handle_column_info, new_int_handle_column_info, new_txn_id_column_info,
-                new_version_column_info,
-            },
-            columnar::Schema,
-            schema_file::{self, SchemaFile},
+            new_common_handle_column_info, new_int_handle_column_info, new_txn_id_column_info,
+            new_version_column_info, Schema, SchemaFile,
         },
         sstable::{File, LocalFile, NO_COMPRESSION},
         ChecksumType,
@@ -457,7 +455,7 @@ impl SchemaManager {
                 }
 
                 let new_schema_file_data =
-                    schema_file::build_schema_file(keyspace_id, schema_version, schemas.unwrap());
+                    columnar::build_schema_file(keyspace_id, schema_version, schemas.unwrap());
                 let file_id = self.ctx.pd.alloc_id()?;
                 let dfs = self.ctx.s3fs.clone();
                 let tx_clone = tx.clone();
@@ -887,9 +885,7 @@ mod tests {
     use bytes::Bytes;
     use kvengine::table::{
         columnar::{
-            builder::{new_int_handle_column_info, new_version_column_info},
-            columnar::Schema,
-            schema_file,
+            build_schema_file, new_int_handle_column_info, new_version_column_info, Schema,
         },
         sstable::LocalFile,
     };
@@ -935,7 +931,7 @@ mod tests {
             };
             schemas.push(schema);
         }
-        let schema_file_data = schema_file::build_schema_file(1234, 100, schemas.clone());
+        let schema_file_data = build_schema_file(1234, 100, schemas.clone());
         write_schema_file_to_local(dir.path(), 1234, 1000, Bytes::from(schema_file_data)).unwrap();
         schemas.push(Schema {
             table_id: 11,
@@ -944,7 +940,7 @@ mod tests {
             txn_id_column: None,
             columns: vec![new_int_handle_column_info()],
         });
-        let schema_file_data = schema_file::build_schema_file(1234, 201, schemas);
+        let schema_file_data = build_schema_file(1234, 201, schemas);
         write_schema_file_to_local(dir.path(), 1234, 1001, Bytes::from(schema_file_data)).unwrap();
 
         // schema_file is the newest schema file of the keyspace.
diff --git a/components/kvengine/src/apply.rs b/components/kvengine/src/apply.rs
index 1148980dd..5d317e20f 100644
--- a/components/kvengine/src/apply.rs
+++ b/components/kvengine/src/apply.rs
@@ -17,7 +17,7 @@ use crate::{
     meta::is_move_down,
     table::{
         blobtable::blobtable::BlobTable,
-        columnar::schema_file::SchemaFile,
+        columnar::{ColumnarFile, SchemaFile},
         memtable::CfTable,
         sstable::{BlockCacheKey, L0Table, LocalFile, SsTable},
         InnerKey, TableExt, TxnFile,
@@ -34,6 +34,7 @@ pub struct ChangeSet {
     pub unloaded_tables: HashMap<u64, FileMeta>,
     pub lock_txn_files: Vec<TxnFile>,
     pub schema_file: Option<SchemaFile>,
+    pub col_files: HashMap<u64, ColumnarFile>,
 }
 
 impl Deref for ChangeSet {
@@ -60,6 +61,7 @@ impl ChangeSet {
             unloaded_tables: HashMap::new(),
             lock_txn_files: vec![],
             schema_file: None,
+            col_files: HashMap::new(),
         }
     }
 
@@ -74,6 +76,9 @@ impl ChangeSet {
         if meta.is_blob_file() {
             let blob_table = BlobTable::new(Arc::new(file))?;
             self.blob_tables.insert(id, blob_table);
+        } else if meta.is_columnar_file() {
+            self.col_files
+                .insert(id, ColumnarFile::open(Arc::new(file))?);
         } else if meta.get_level() == 0 {
             let l0_table = L0Table::new(Arc::new(file), Some(cache), false, encryption_key)?;
             self.l0_tables.insert(id, l0_table.unwrap());
@@ -101,6 +106,7 @@ pub(crate) fn create_snapshot_tables(
     HashMap<u64, BlobTable>,
     [ShardCf; 3],
     Vec<TxnFile>,
+    ColumnarLevels,
 ) {
     // Note: Some tables in `snap` will not exist in `tables` if it's not necessary
     // to load from DFS.
@@ -157,7 +163,30 @@ pub(crate) fn create_snapshot_tables(
         let scf = &mut scf_builders.as_mut_slice()[cf];
         scfs[cf] = scf.build();
     }
-    (l0_tbls, blob_tbl_map, scfs, tables.lock_txn_files.clone())
+    let mut col_levels = ColumnarLevels::new();
+    for col_create in snap.get_columnar_creates() {
+        if let Some(col_file) = tables.col_files.get(&col_create.id) {
+            col_levels.add_file(col_create.level as usize, col_file.clone());
+        } else {
+            assert!(
+                not_all_tables_loaded,
+                "columnar_create: {:?}, tables: {:?}",
+                col_create, tables,
+            );
+        }
+    }
+    for &l0_id in snap.get_unconverted_l0s() {
+        let unconverted_l0 = l0_tbls.iter().find(|l0| l0.id() == l0_id).unwrap().clone();
+        col_levels.unconverted_l0s.push(unconverted_l0);
+    }
+    col_levels.sort();
+    (
+        l0_tbls,
+        blob_tbl_map,
+        scfs,
+        tables.lock_txn_files.clone(),
+        col_levels,
+    )
 }
 
 impl EngineCore {
@@ -199,6 +228,7 @@ impl EngineCore {
             || cs.has_truncate_ts()
             || cs.has_trim_over_bound()
             || cs.has_major_compaction()
+            || cs.has_columnar_compaction()
         {
             if cs.has_compaction() {
                 self.apply_compaction(&shard, &cs);
@@ -210,6 +240,8 @@ impl EngineCore {
                 self.apply_trim_over_bound(&shard, &cs);
             } else if cs.has_major_compaction() {
                 self.apply_major_compaction(&shard, &cs);
+            } else if cs.has_columnar_compaction() {
+                self.apply_columnar_compaction(&shard, &cs);
             }
             store_bool(&shard.compacting, false);
             self.send_compact_msg(CompactMsg::Applied(IdVer::new(shard.id, shard.ver)));
@@ -269,6 +301,10 @@ impl EngineCore {
             let mut new_l0_tbls = Vec::with_capacity(old_data.l0_tbls.len() + l0s.len());
             new_l0_tbls.extend_from_slice(l0s.as_slice());
             new_l0_tbls.extend_from_slice(old_data.l0_tbls.as_slice());
+            let mut col_levels = old_data.col_levels.clone();
+            if old_data.schema_file.is_some() {
+                col_levels.unconverted_l0s.extend_from_slice(l0s.as_slice());
+            }
 
             let new_data = ShardData::new(
                 old_data.range.clone(),
@@ -281,6 +317,7 @@ impl EngineCore {
                 old_data.limiter.clone(),
                 old_data.update_counter + 1,
                 old_data.schema_file.clone(),
+                col_levels,
             );
             shard.set_data(new_data);
             self.send_free_mem_msg(FreeMemMsg::FreeMem(last));
@@ -304,11 +341,13 @@ impl EngineCore {
                     old_data.limiter.clone(),
                     old_data.update_counter + 1,
                     old_data.schema_file.clone(),
+                    old_data.col_levels.clone(),
                 );
                 shard.set_data(new_data);
                 self.send_free_mem_msg(FreeMemMsg::FreeMem(last));
             }
         }
+        store_u64(&shard.snap_version, flush.get_version());
         shard.clear_finished_txn_file_refs(flush.version);
     }
 
@@ -319,7 +358,7 @@ impl EngineCore {
 
         // `lock_txn_files` is ignored because it does not depend on initial flush to
         // keep consistency between peers, as only target region has txn file locks.
-        let (l0s, blob_tbl_map, scfs, _lock_txn_files) =
+        let (l0s, blob_tbl_map, scfs, _lock_txn_files, col_levels) =
             create_snapshot_tables(initial_flush, cs, self.opts.for_restore);
         let mut max_flushed_mem_tbl_version = 0;
         mem_tbls.retain(|x| {
@@ -345,6 +384,7 @@ impl EngineCore {
             data.limiter.clone(),
             data.update_counter + 1,
             data.schema_file.clone(),
+            col_levels,
         );
         info!("{} apply_initial_flush", shard.tag();
             "seq" => cs.sequence,
@@ -419,6 +459,7 @@ impl EngineCore {
             data.limiter.clone(),
             data.update_counter + 1,
             data.schema_file.clone(),
+            data.col_levels.clone(),
         );
         shard.set_data(new_data);
         self.remove_dfs_files(shard, del_files);
@@ -521,37 +562,52 @@ impl EngineCore {
             data.limiter.clone(),
             data.update_counter + 1,
             data.schema_file.clone(),
+            data.col_levels.clone(),
         );
         shard.set_data(new_data);
         self.remove_dfs_files(shard, del_file_is_subrange);
         shard.set_property(MANUAL_MAJOR_COMPACTION, MANUAL_MAJOR_COMPACTION_DISABLE);
     }
 
-    fn get_sstables_from_table_change(
+    fn get_tables_from_table_change(
         &self,
         data: &ShardData,
         cs: &ChangeSet,
         tc: &pb::TableChange,
         del_files: &mut HashMap<u64, bool>,
-    ) -> (Vec<L0Table>, [ShardCf; 3]) {
+    ) -> (Vec<L0Table>, [ShardCf; 3], ColumnarLevels) {
         let mut new_l0s = data.l0_tbls.clone();
         let mut new_cfs = data.cfs.clone();
+        let mut new_col_levels = data.col_levels.clone();
         // Group files by cf and level.
         let mut grouped = HashMap::new();
+        let mut all_deletes = HashSet::new();
         for deleted in tc.get_table_deletes() {
             grouped
                 .entry((deleted.get_cf() as usize, deleted.get_level() as usize))
                 .or_insert_with(|| (Vec::new(), Vec::new()))
                 .0
                 .push(deleted.get_id());
+            all_deletes.insert(deleted.get_id());
+        }
+        for col_level in &mut new_col_levels.levels {
+            col_level
+                .files
+                .retain(|c| !all_deletes.contains(&c.get_file().id()));
         }
         for created in tc.get_table_creates() {
+            if created.columnar_tables > 0 {
+                let col_file = cs.col_files.get(&created.get_id()).unwrap().clone();
+                new_col_levels.add_file(created.get_level() as usize, col_file);
+                continue;
+            }
             grouped
                 .entry((created.get_cf() as usize, created.get_level() as usize))
                 .or_insert_with(|| (Vec::new(), Vec::new()))
                 .1
                 .push(created.get_id());
         }
+        new_col_levels.sort();
 
         for ((cf, level), (deletes, creates)) in grouped {
             if level == 0 {
@@ -591,7 +647,7 @@ impl EngineCore {
             }
         }
 
-        (new_l0s, new_cfs)
+        (new_l0s, new_cfs, new_col_levels)
     }
 
     fn apply_destroy_range(&self, shard: &Shard, cs: &ChangeSet) {
@@ -599,7 +655,8 @@ impl EngineCore {
         let data = shard.get_data();
         let tc = cs.get_destroy_range();
         let mut del_files = HashMap::new();
-        let (new_l0s, new_cfs) = self.get_sstables_from_table_change(&data, cs, tc, &mut del_files);
+        let (new_l0s, new_cfs, col_levels) =
+            self.get_tables_from_table_change(&data, cs, tc, &mut del_files);
 
         let new_data = ShardData::new(
             data.range.clone(),
@@ -612,6 +669,7 @@ impl EngineCore {
             data.limiter.clone(),
             data.update_counter + 1,
             data.schema_file.clone(),
+            col_levels,
         );
         assert_eq!(cs.get_property_key(), DEL_PREFIXES_KEY);
         let done = DeletePrefixes::unmarshal(cs.get_property_value(), shard.inner_key_off);
@@ -628,7 +686,8 @@ impl EngineCore {
         let data = shard.get_data();
         let tc = cs.get_truncate_ts();
         let mut del_files = HashMap::new();
-        let (new_l0s, new_cfs) = self.get_sstables_from_table_change(&data, cs, tc, &mut del_files);
+        let (new_l0s, new_cfs, col_levels) =
+            self.get_tables_from_table_change(&data, cs, tc, &mut del_files);
         assert_eq!(cs.get_property_key(), TRUNCATE_TS_KEY);
         let new_data = ShardData::new(
             data.range.clone(),
@@ -641,6 +700,7 @@ impl EngineCore {
             data.limiter.clone(),
             data.update_counter + 1,
             data.schema_file.clone(),
+            col_levels,
         );
         shard.set_data(new_data);
         let truncated_ts = TruncateTs::unmarshal(cs.get_property_value());
@@ -658,7 +718,8 @@ impl EngineCore {
         let data = shard.get_data();
         let tc = cs.get_trim_over_bound();
         let mut del_files = HashMap::new();
-        let (new_l0s, new_cfs) = self.get_sstables_from_table_change(&data, cs, tc, &mut del_files);
+        let (new_l0s, new_cfs, col_levels) =
+            self.get_tables_from_table_change(&data, cs, tc, &mut del_files);
         let new_data = ShardData::new(
             data.range.clone(),
             data.mem_tbls.clone(),
@@ -670,6 +731,7 @@ impl EngineCore {
             data.limiter.clone(),
             data.update_counter + 1,
             data.schema_file.clone(),
+            col_levels,
         );
         shard.set_data(new_data);
         shard.set_property(TRIM_OVER_BOUND, TRIM_OVER_BOUND_DISABLE);
@@ -810,6 +872,7 @@ impl EngineCore {
             old_data.limiter.clone(),
             old_data.update_counter + 1,
             old_data.schema_file.clone(),
+            old_data.col_levels.clone(),
         );
         shard.set_data(new_data);
         Ok(())
@@ -839,8 +902,9 @@ impl EngineCore {
         );
         let snap_data = new_shard.get_data();
         let old_data = old_shard.get_data();
-        let (l0_tbls, blob_tbl_map, cfs, _) =
+        let (l0_tbls, blob_tbl_map, cfs, _, col_levels) =
             create_snapshot_tables(cs.get_restore_shard(), cs, self.opts.for_restore);
+        let schema_file = cs.schema_file.clone();
         let new_data = ShardData::new(
             snap_data.range.clone(),
             vec![CfTable::new()],
@@ -851,7 +915,8 @@ impl EngineCore {
             vec![],
             old_data.limiter.clone(),
             NEW_DATA_UPDATE_COUNTER,
-            old_data.schema_file.clone(),
+            schema_file,
+            col_levels,
         );
         new_shard.set_data(new_data);
         new_shard.set_active(old_shard.is_active());
@@ -892,7 +957,43 @@ impl EngineCore {
             old_data.limiter.clone(),
             old_data.update_counter + 1,
             cs.schema_file.clone(),
+            old_data.col_levels.clone(),
+        );
+        shard.set_data(new_data);
+    }
+
+    fn apply_columnar_compaction(&self, shard: &Shard, cs: &ChangeSet) {
+        let col_comp = cs.get_columnar_compaction();
+        let col_change = col_comp.get_columnar_change();
+        let old_data = shard.get_data();
+        let mut new_col_levels = old_data.col_levels.clone();
+        let deletes: HashSet<u64> = col_change
+            .get_table_deletes()
+            .iter()
+            .map(|del| del.get_id())
+            .collect();
+        new_col_levels.retain(|c| !deletes.contains(&c.get_file().id()));
+        for create in col_change.get_table_creates() {
+            let col_file = cs.col_files.get(&create.get_id()).unwrap().clone();
+            new_col_levels.add_file(create.get_level() as usize, col_file);
+        }
+        new_col_levels
+            .unconverted_l0s
+            .retain(|l0| !col_comp.row_l0s.contains(&l0.id()));
+        let new_data = ShardData::new(
+            old_data.range.clone(),
+            old_data.mem_tbls.clone(),
+            old_data.l0_tbls.clone(),
+            old_data.blob_tbl_map.clone(),
+            old_data.cfs.clone(),
+            old_data.unloaded_tbls.clone(),
+            old_data.lock_txn_files.clone(),
+            old_data.limiter.clone(),
+            old_data.update_counter + 1,
+            old_data.schema_file.clone(),
+            new_col_levels,
         );
         shard.set_data(new_data);
+        store_u64(&shard.col_snap_version, col_comp.get_snap_version());
     }
 }
diff --git a/components/kvengine/src/compaction.rs b/components/kvengine/src/compaction.rs
index f8a122149..4aa26ff86 100644
--- a/components/kvengine/src/compaction.rs
+++ b/components/kvengine/src/compaction.rs
@@ -11,6 +11,7 @@ use std::{
     time::{Duration, Instant},
 };
 
+use api_version::ApiV2;
 use bytes::{Buf, Bytes, BytesMut};
 use cloud_encryption::{EncryptionKey, MasterKey};
 use http::StatusCode;
@@ -24,12 +25,16 @@ use tikv_util::mpsc;
 
 use crate::{
     dfs,
+    dfs::FileType,
     table::{
         blobtable::{
             blobtable::BlobTable,
             builder::{BlobTableBuildOptions, BlobTableBuilder},
         },
-        columnar::builder::ColumnarTableBuildOptions,
+        columnar::{
+            Block, ColumnarFileBuilder, ColumnarMergeReader, ColumnarReader,
+            ColumnarRowTableReader, ColumnarTableBuildOptions, ColumnarTableBuilder, SchemaFile,
+        },
         get_tables_in_range,
         sstable::{
             self, builder::TableBuilderOptions, File, InMemFile, L0Builder, LocalFile, SsTable,
@@ -421,8 +426,10 @@ pub struct L1PlusCompaction {
 pub struct ColumnarCompaction {
     level: u32,
     safe_ts: u64,
-    source_row_tables: Vec<(u32, u64)>,      // (level, id)
-    source_columnar_tables: Vec<(u32, u64)>, // (level, id)
+    snap_version: u64,
+    source_row_files: Vec<(u32, u64)>,      // (level, id)
+    source_columnar_files: Vec<(u32, u64)>, // (level, id)
+    schema_file_id: u64,
     columnar_config: ColumnarTableBuildOptions,
 }
 
@@ -541,6 +548,7 @@ impl Engine {
             Some(CompactionPriority::DestroyRange) => self.destroy_range(&shard).transpose(),
             Some(CompactionPriority::TruncateTs) => self.truncate_ts(&shard).transpose(),
             Some(CompactionPriority::TrimOverBound) => self.trim_over_bound(&shard).transpose(),
+            Some(CompactionPriority::L0ToColumnar) => self.trigger_l0_to_columnar(&shard),
             None => {
                 info!("Shard {} is not urgent for compaction", tag);
                 store_bool(&shard.compacting, false);
@@ -1331,6 +1339,40 @@ impl Engine {
         Some(self.comp_client.compact(req))
     }
 
+    pub(crate) fn trigger_l0_to_columnar(&self, shard: &Shard) -> Option<Result<pb::ChangeSet>> {
+        let tag = shard.tag();
+        let data = shard.get_data();
+        if data.col_levels.unconverted_l0s.is_empty() {
+            store_bool(&shard.compacting, false);
+            return None;
+        }
+        let schema_file = shard.get_schema_file()?;
+        let mut req = self.new_compact_request_with_shard(shard, 0, 0);
+        let mut source_row_tables = vec![];
+        let mut snap_version = 0;
+        for l0 in &data.col_levels.unconverted_l0s {
+            source_row_tables.push((0, l0.id()));
+            snap_version = snap_version.max(l0.version());
+        }
+        let num_l0s = source_row_tables.len();
+        let columnar_compaction = ColumnarCompaction {
+            level: 0,
+            safe_ts: self.get_keyspace_gc_safepoint_v2(shard.keyspace_id),
+            snap_version,
+            source_row_files: source_row_tables,
+            source_columnar_files: vec![],
+            schema_file_id: schema_file.get_file_id(),
+            columnar_config: self.opts.columnar_build_options,
+        };
+        req.compaction_tp = CompactionType::Columnar(columnar_compaction);
+        self.set_alloc_ids_for_request(&mut req, num_l0s, num_l0s);
+        info!(
+            "start covert L0 to columnar for {}, num_l0s {}",
+            tag, num_l0s
+        );
+        Some(self.comp_client.compact(req))
+    }
+
     pub(crate) fn handle_compact_response(&self, cs: pb::ChangeSet) {
         self.meta_change_listener.on_change_set(cs);
     }
@@ -1344,6 +1386,7 @@ pub(crate) enum CompactionPriority {
     DestroyRange,
     TruncateTs,
     TrimOverBound,
+    L0ToColumnar,
 }
 
 impl CompactionPriority {
@@ -1355,6 +1398,7 @@ impl CompactionPriority {
             CompactionPriority::DestroyRange => f64::MAX,
             CompactionPriority::TruncateTs => f64::MAX,
             CompactionPriority::TrimOverBound => f64::MAX,
+            CompactionPriority::L0ToColumnar => 2.0,
         }
     }
 
@@ -1366,6 +1410,7 @@ impl CompactionPriority {
             CompactionPriority::DestroyRange => -1,
             CompactionPriority::TruncateTs => -1,
             CompactionPriority::TrimOverBound => -1,
+            CompactionPriority::L0ToColumnar => 0,
         }
     }
 
@@ -1377,6 +1422,7 @@ impl CompactionPriority {
             CompactionPriority::DestroyRange => -1,
             CompactionPriority::TruncateTs => -1,
             CompactionPriority::TrimOverBound => -1,
+            CompactionPriority::L0ToColumnar => 0,
         }
     }
 }
@@ -1740,10 +1786,14 @@ fn local_compact(ctx: &CompactionCtx) -> Result<pb::ChangeSet> {
         CompactionType::Major(major_compaction) => {
             cs.set_major_compaction(major_compact_v3(ctx, major_compaction, &mut allocate_id)?);
         }
-        CompactionType::Unknown => unreachable!(),
-        CompactionType::Columnar(_) => {
-            unimplemented!()
+        CompactionType::Columnar(columnar_compaction) => {
+            cs.set_columnar_compaction(columnar_compact(
+                ctx,
+                columnar_compaction,
+                &mut allocate_id,
+            )?);
         }
+        CompactionType::Unknown => unreachable!(),
     }
     Ok(cs)
 }
@@ -2130,7 +2180,7 @@ fn compact_trim_over_bound(
 }
 
 enum FilePersistResult {
-    SsTableCreate(pb::TableCreate),
+    TableCreate(pb::TableCreate),
     BlobTableCreate(pb::BlobCreate),
 }
 
@@ -2158,7 +2208,7 @@ fn persist_sst(
             fs_clone
                 .create(id, buf.into(), opts)
                 .await
-                .map(|_| FilePersistResult::SsTableCreate(tbl_create)),
+                .map(|_| FilePersistResult::TableCreate(tbl_create)),
         )
         .unwrap();
     });
@@ -2189,6 +2239,33 @@ fn persist_blob_table(
     });
 }
 
+fn persist_columnar_file(
+    target_lvl: u32,
+    builder: &mut ColumnarFileBuilder,
+    tx: mpsc::Sender<dfs::Result<pb::TableCreate>>,
+    fs: Arc<dyn dfs::Dfs>,
+    opts: dfs::Options,
+) {
+    let id = builder.file_id;
+    let buf = builder.build();
+    let mut table_create = pb::TableCreate::new();
+    table_create.set_id(id);
+    table_create.set_smallest(builder.smallest.clone());
+    table_create.set_biggest(builder.biggest.clone());
+    table_create.set_columnar_tables(builder.num_tables() as u32);
+    table_create.set_level(target_lvl);
+    let fs_clone = fs.clone();
+    fs.get_runtime().spawn(async move {
+        tx.send(
+            fs_clone
+                .create(id, buf.into(), opts.with_type(FileType::Columnar))
+                .await
+                .map(|_| table_create),
+        )
+        .unwrap();
+    });
+}
+
 fn compact_for_cf(
     ctx: &CompactionCtx,
     iter: &mut Box<dyn Iterator>,
@@ -2411,7 +2488,7 @@ fn compact_for_cf(
     for _ in 0..cnt {
         match rx.recv().unwrap() {
             Err(err) => errors.push(err),
-            Ok(FilePersistResult::SsTableCreate(tbl_create)) => {
+            Ok(FilePersistResult::TableCreate(tbl_create)) => {
                 sst_creates.push(tbl_create);
             }
             Ok(FilePersistResult::BlobTableCreate(blob_table_create)) => {
@@ -2642,6 +2719,140 @@ fn major_compact_v3(
     Ok(ret)
 }
 
+fn columnar_compact(
+    ctx: &CompactionCtx,
+    columnar_compaction: &ColumnarCompaction,
+    allocate_id: &mut dyn FnMut() -> u64,
+) -> Result<pb::ColumnarCompaction> {
+    if !columnar_compaction.source_row_files.is_empty() {
+        convert_row_file_to_columnar_file(ctx, columnar_compaction, allocate_id)
+    } else {
+        compact_columnar_files(ctx, columnar_compaction, allocate_id)
+    }
+}
+
+fn convert_row_file_to_columnar_file(
+    ctx: &CompactionCtx,
+    columnar_compaction: &ColumnarCompaction,
+    allocate_id: &mut dyn FnMut() -> u64,
+) -> Result<pb::ColumnarCompaction> {
+    let mut ret = pb::ColumnarCompaction::new();
+    ret.set_snap_version(columnar_compaction.snap_version);
+    let row_l0s: Vec<u64> = columnar_compaction
+        .source_row_files
+        .iter()
+        .filter(|(lvl, _)| *lvl == 0)
+        .map(|(_, id)| *id)
+        .collect();
+    ret.set_row_l0s(row_l0s);
+    let opts = dfs::Options::default().with_type(FileType::Schema);
+    let runtime = ctx.dfs.get_runtime();
+    let schema_file_id = columnar_compaction.schema_file_id;
+    let schema_file_data = runtime.block_on(ctx.dfs.read_file(schema_file_id, opts))?;
+    let schema_file = SchemaFile::open(Arc::new(InMemFile::new(schema_file_id, schema_file_data)))?;
+    let l0_ids: Vec<u64> = columnar_compaction
+        .source_row_files
+        .iter()
+        .map(|(_, id)| *id)
+        .collect();
+    let opts = dfs::Options::default().with_shard(ctx.req.shard_id, ctx.req.shard_ver);
+    let l0_files = load_table_files(
+        &l0_ids,
+        ctx.dfs.clone(),
+        opts,
+        ctx.local_dir.as_ref(),
+        ctx.for_restore,
+    )?;
+    let l0_tbls = files_to_l0_tables(l0_files, ctx.encryption_key.clone());
+    let smallest = l0_tbls.iter().map(|l0| l0.smallest()).min().unwrap();
+    let biggest = l0_tbls.iter().map(|l0| l0.biggest()).max().unwrap();
+    let overlap_tables = schema_file.overlap_tables(smallest, biggest);
+    if overlap_tables.is_empty() {
+        return Ok(ret);
+    }
+    let keyspace_id = ApiV2::get_u32_keyspace_id_by_key(&ctx.req.outer_start).unwrap_or_default();
+    let mut file_builder =
+        ColumnarFileBuilder::new(allocate_id(), Some(columnar_compaction.snap_version));
+    let pack_max_row_count = columnar_compaction.columnar_config.pack_max_row_count;
+    let max_table_size = columnar_compaction.columnar_config.max_columnar_table_size;
+    let mut cnt = 0;
+    let (tx, rx) = mpsc::bounded(ctx.req.file_ids.len());
+    for overlap_table in overlap_tables {
+        let schema = schema_file.get_table(overlap_table).unwrap();
+        let mut columnar_readers: Vec<Box<dyn ColumnarReader>> = vec![];
+        for l0_tbl in &l0_tbls {
+            if let Some(tbl) = l0_tbl.get_cf(WRITE_CF) {
+                let iter = tbl.new_iterator(false, false);
+                let reader = ColumnarRowTableReader::new(keyspace_id, schema.clone(), iter, true);
+                columnar_readers.push(Box::new(reader));
+            }
+        }
+        if columnar_readers.is_empty() {
+            continue;
+        }
+        let mut merge_reader = ColumnarMergeReader::new(schema.clone(), columnar_readers);
+        let mut block = Block::new(schema);
+        merge_reader.seek(&[])?;
+        let mut res = merge_reader.read(&mut block, pack_max_row_count)?;
+        let mut row_count = 0;
+        let mut tbl_builder =
+            ColumnarTableBuilder::new(schema.clone(), columnar_compaction.columnar_config, false);
+        let mut block_offset = 0;
+        while res > 0 {
+            row_count += res;
+            block_offset = tbl_builder.append_block(&block, block_offset);
+            if block_offset < block.length() {
+                res = block.length() - block_offset;
+                let estimated_size = tbl_builder.get_estimated_size() + file_builder.estimated_size;
+                if estimated_size > max_table_size {
+                    file_builder.add_table(tbl_builder);
+                    cnt += 1;
+                    persist_columnar_file(0, &mut file_builder, tx.clone(), ctx.dfs.clone(), opts);
+                    file_builder.reset(allocate_id());
+                    tbl_builder = ColumnarTableBuilder::new(
+                        schema.clone(),
+                        columnar_compaction.columnar_config,
+                        false,
+                    );
+                }
+            } else {
+                block.reset();
+                block_offset = 0;
+                res = merge_reader.read(&mut block, pack_max_row_count)?;
+            }
+        }
+        if row_count > 0 {
+            file_builder.add_table(tbl_builder);
+        }
+    }
+    if file_builder.num_tables() > 0 {
+        cnt += 1;
+        persist_columnar_file(0, &mut file_builder, tx, ctx.dfs.clone(), opts);
+    }
+    let change = ret.mut_columnar_change();
+    let mut errors = vec![];
+    for _ in 0..cnt {
+        match rx.recv().unwrap() {
+            Err(err) => errors.push(err),
+            Ok(tbl_create) => {
+                change.mut_table_creates().push(tbl_create);
+            }
+        }
+    }
+    if !errors.is_empty() {
+        return Err(errors.pop().unwrap().into());
+    }
+    Ok(ret)
+}
+
+fn compact_columnar_files(
+    _ctx: &CompactionCtx,
+    _columnar_compaction: &ColumnarCompaction,
+    _allocate_id: &mut dyn FnMut() -> u64,
+) -> Result<pb::ColumnarCompaction> {
+    unimplemented!()
+}
+
 pub(crate) enum CompactMsg {
     /// The shard is ready to be compacted / destroyed range / truncated ts.
     Compact(IdVer),
diff --git a/components/kvengine/src/config.rs b/components/kvengine/src/config.rs
index 4a20fea9e..832256f81 100644
--- a/components/kvengine/src/config.rs
+++ b/components/kvengine/src/config.rs
@@ -7,7 +7,9 @@ use std::{
 
 use tikv_util::config::ReadableDuration;
 
-use crate::table::{blobtable::builder::BlobTableBuildOptions, ChecksumType};
+use crate::table::{
+    blobtable::builder::BlobTableBuildOptions, columnar::ColumnarTableBuildOptions, ChecksumType,
+};
 
 pub(crate) const DEFAULT_COMPACTION_REQUEST_VERSION: u32 = 3;
 pub(crate) const DEFAULT_COMPACTION_TOMBS_RATIO: f64 = 0.2;
@@ -81,6 +83,8 @@ pub struct Config {
     // will meet a "ValueAfterTable" error.
     // See https://docs.rs/toml/0.5.11/toml/ser/enum.Error.html#variant.ValueAfterTable.
     pub blob_table_build_options: BlobTableBuildOptions,
+
+    pub columnar_table_build_options: ColumnarTableBuildOptions,
 }
 
 impl Default for Config {
@@ -98,6 +102,7 @@ impl Default for Config {
             checksum_type: ChecksumType::Crc32,
             blob_table_build_options: Default::default(),
             per_keyspace_configs: vec![],
+            columnar_table_build_options: Default::default(),
         }
     }
 }
diff --git a/components/kvengine/src/engine.rs b/components/kvengine/src/engine.rs
index a717b4ec3..47416e2fd 100644
--- a/components/kvengine/src/engine.rs
+++ b/components/kvengine/src/engine.rs
@@ -33,7 +33,7 @@ use crate::{
     limiter::{RegionLimiter, StoreLimiter},
     meta::ShardMeta,
     table::{
-        columnar::schema_file::SchemaFile,
+        columnar::SchemaFile,
         memtable::CfTable,
         sstable::{BlockCacheKey, MAGIC_NUMBER, ZSTD_COMPRESSION},
         InnerKey,
@@ -393,7 +393,7 @@ impl EngineCore {
             shard.load_mem_table_version(),
             &cs,
         );
-        let (l0s, blob_tbls, scfs, lock_txn_files) =
+        let (l0s, blob_tbls, scfs, lock_txn_files, col_lvls) =
             create_snapshot_tables(cs.get_snapshot(), &cs, self.opts.for_restore);
         let schema_file = cs.get_snapshot().has_schema_meta().then(|| {
             let schema_file_id = cs.get_snapshot().get_schema_meta().get_file_id();
@@ -413,6 +413,7 @@ impl EngineCore {
             RegionLimiter::new((&shard.opt.flow_control).into()),
             NEW_DATA_UPDATE_COUNTER,
             schema_file,
+            col_lvls,
         );
         shard.set_data(data);
         shard
@@ -889,6 +890,10 @@ pub fn new_schema_filename(file_id: u64) -> PathBuf {
     PathBuf::from(format!("{:016x}.schema", file_id))
 }
 
+pub fn new_columnar_filename(file_id: u64) -> PathBuf {
+    PathBuf::from(format!("{:016x}.col", file_id))
+}
+
 pub(crate) enum FreeMemMsg {
     /// Free CfTable
     FreeMem(CfTable),
diff --git a/components/kvengine/src/flush.rs b/components/kvengine/src/flush.rs
index 7e9a0e971..13db68133 100644
--- a/components/kvengine/src/flush.rs
+++ b/components/kvengine/src/flush.rs
@@ -11,7 +11,7 @@ use bytes::{Bytes, BytesMut};
 use cloud_encryption::EncryptionKey;
 use fail::fail_point;
 use kvenginepb as pb;
-use kvenginepb::L0Create;
+use kvenginepb::{L0Create, SchemaMeta};
 use tikv_util::{
     info, mpsc,
     time::{monotonic_raw_now, timespec_to_ns},
@@ -258,6 +258,28 @@ impl Engine {
             blob_create.set_biggest(blob.biggest_key().to_vec());
             initial_flush.mut_blob_creates().push(blob_create);
         }
+        if let Some(schema_file) = &flush.shard_data.schema_file {
+            // After split, the schema file may not overlap the schema file anymore.
+            if schema_file.overlap(
+                &task.range.outer_start,
+                &task.range.outer_end,
+                task.range.keyspace_id,
+            ) {
+                let mut schema_meta = SchemaMeta::new();
+                schema_meta.set_file_id(schema_file.get_file_id());
+                schema_meta.set_version(schema_file.get_version());
+                schema_meta.set_keyspace_id(schema_file.get_keyspace_id());
+                initial_flush.set_schema_meta(schema_meta);
+                let unconverted_l0s = flush
+                    .shard_data
+                    .col_levels
+                    .unconverted_l0s
+                    .iter()
+                    .map(|l0| l0.id())
+                    .collect();
+                initial_flush.set_unconverted_l0s(unconverted_l0s);
+            }
+        }
         let (tx, rx) = mpsc::unbounded();
         let mut send_cnt = 0;
         for m in &flush.mem_tbls {
@@ -271,6 +293,9 @@ impl Engine {
         for _ in 0..send_cnt {
             match rx.recv().unwrap() {
                 Ok(l0_create) => {
+                    if initial_flush.has_schema_meta() {
+                        initial_flush.mut_unconverted_l0s().push(l0_create.get_id());
+                    }
                     initial_flush.mut_l0_creates().push(l0_create);
                 }
                 Err(err) => {
diff --git a/components/kvengine/src/meta.rs b/components/kvengine/src/meta.rs
index 97bd26a39..43eaa36ba 100644
--- a/components/kvengine/src/meta.rs
+++ b/components/kvengine/src/meta.rs
@@ -39,6 +39,8 @@ pub struct ShardMeta {
     pub parent: Option<Box<ShardMeta>>,
     pub schema_file_id: u64,
     pub schema_file_ver: i64,
+    pub columnar_snap_version: u64,
+    pub unconverted_l0s: Vec<u64>,
 
     pub(crate) txn_file_locks: TxnFileLocks,
 }
@@ -73,29 +75,22 @@ impl ShardMeta {
             base_version: snap.base_version,
             data_sequence: snap.data_sequence,
             max_ts: snap.max_ts,
+            columnar_snap_version: snap.columnar_snap_version,
+            unconverted_l0s: snap.unconverted_l0s.clone(),
             txn_file_locks,
             ..Default::default()
         };
         for l0 in snap.get_l0_creates() {
-            meta.add_file(l0.id, -1, 0, l0.get_smallest(), l0.get_biggest());
+            meta.add_file(l0.id, FileMeta::from_l0_table(l0));
         }
         for blob in snap.get_blob_creates() {
-            meta.add_file(
-                blob.id,
-                -1,
-                BLOB_LEVEL,
-                blob.get_smallest(),
-                blob.get_biggest(),
-            );
+            meta.add_file(blob.id, FileMeta::from_blob_table(blob));
         }
         for tbl in snap.get_table_creates() {
-            meta.add_file(
-                tbl.id,
-                tbl.cf,
-                tbl.level,
-                tbl.get_smallest(),
-                tbl.get_biggest(),
-            );
+            meta.add_file(tbl.id, FileMeta::from_table(tbl));
+        }
+        for col in snap.get_columnar_creates() {
+            meta.add_file(col.get_id(), FileMeta::from_table(col));
         }
         if cs.has_parent() {
             let parent_meta = Box::new(Self::new(engine_id, cs.get_parent()));
@@ -161,9 +156,8 @@ impl ShardMeta {
         fm.level = level as u8;
     }
 
-    pub fn add_file(&mut self, id: u64, cf: i32, level: u32, smallest: &[u8], biggest: &[u8]) {
-        self.files
-            .insert(id, FileMeta::new(cf, level, smallest, biggest, 0));
+    pub fn add_file(&mut self, id: u64, file_meta: FileMeta) {
+        self.files.insert(id, file_meta);
     }
 
     fn delete_file(&mut self, id: u64, level: u32) {
@@ -242,6 +236,10 @@ impl ShardMeta {
             self.schema_file_ver = sm.get_version();
             return;
         }
+        if cs.has_columnar_compaction() {
+            self.apply_columnar_compaction(cs.get_columnar_compaction());
+            return;
+        }
         if !cs.get_property_key().is_empty() {
             if cs.get_property_merge() {
                 // Now only DEL_PREFIXES_KEY is mergeable.
@@ -486,12 +484,18 @@ impl ShardMeta {
     fn apply_flush(&mut self, cs: &pb::ChangeSet) {
         let flush = cs.get_flush();
         self.apply_properties(flush.get_properties());
+        let mut new_l0s = vec![];
         if flush.has_l0_create() {
             let l0 = flush.get_l0_create();
-            self.add_file(l0.id, -1, 0, l0.get_smallest(), l0.get_biggest());
+            self.add_file(l0.id, FileMeta::from_l0_table(l0));
+            new_l0s.push(l0.id);
         }
         for l0 in flush.get_l0_creates() {
-            self.add_file(l0.id, -1, 0, l0.get_smallest(), l0.get_biggest());
+            self.add_file(l0.id, FileMeta::from_l0_table(l0));
+            new_l0s.push(l0.id);
+        }
+        if self.schema_file_id > 0 {
+            self.unconverted_l0s.extend_from_slice(&new_l0s);
         }
         let new_data_seq = flush.get_version() - self.base_version;
         if self.data_sequence < new_data_seq {
@@ -539,22 +543,10 @@ impl ShardMeta {
             self.delete_file(*id, comp.level + 1);
         }
         for tbl in comp.get_table_creates() {
-            self.add_file(
-                tbl.id,
-                tbl.cf,
-                tbl.level,
-                tbl.get_smallest(),
-                tbl.get_biggest(),
-            )
+            self.add_file(tbl.get_id(), FileMeta::from_table(tbl));
         }
         for blob_table in comp.get_blob_tables() {
-            self.add_file(
-                blob_table.get_id(),
-                -1,
-                BLOB_LEVEL,
-                blob_table.get_smallest(),
-                blob_table.get_biggest(),
-            );
+            self.add_file(blob_table.get_id(), FileMeta::from_blob_table(blob_table));
         }
     }
 
@@ -563,25 +555,13 @@ impl ShardMeta {
             self.delete_file(delete.get_id(), delete.get_level());
         }
         for create in comp.get_sstable_change().get_table_creates() {
-            self.add_file(
-                create.id,
-                create.cf,
-                create.level,
-                create.get_smallest(),
-                create.get_biggest(),
-            );
+            self.add_file(create.id, FileMeta::from_table(create));
         }
         for delete in comp.get_old_blob_tables() {
             self.delete_file(*delete, BLOB_LEVEL);
         }
         for create in comp.get_new_blob_tables() {
-            self.add_file(
-                create.get_id(),
-                -1,
-                BLOB_LEVEL,
-                create.get_smallest(),
-                create.get_biggest(),
-            );
+            self.add_file(create.get_id(), FileMeta::from_blob_table(create));
         }
     }
 
@@ -600,13 +580,7 @@ impl ShardMeta {
             self.delete_file(deleted.get_id(), deleted.get_level());
         }
         for created in tc.get_table_creates() {
-            self.add_file(
-                created.id,
-                created.cf,
-                created.level,
-                created.get_smallest(),
-                created.get_biggest(),
-            );
+            self.add_file(created.id, FileMeta::from_table(created));
         }
     }
 
@@ -666,25 +640,13 @@ impl ShardMeta {
         self.max_ts = std::cmp::max(self.max_ts, ingest_files.max_ts);
         self.apply_properties(ingest_files.get_properties());
         for tbl in ingest_files.get_table_creates() {
-            self.add_file(tbl.id, 0, tbl.level, tbl.get_smallest(), tbl.get_biggest());
+            self.add_file(tbl.id, FileMeta::from_table(tbl));
         }
         for l0_tbl in ingest_files.get_l0_creates() {
-            self.add_file(
-                l0_tbl.id,
-                -1,
-                0,
-                l0_tbl.get_smallest(),
-                l0_tbl.get_biggest(),
-            );
+            self.add_file(l0_tbl.id, FileMeta::from_l0_table(l0_tbl));
         }
         for blob_tbl in ingest_files.get_blob_creates() {
-            self.add_file(
-                blob_tbl.id,
-                -1,
-                BLOB_LEVEL,
-                blob_tbl.get_smallest(),
-                blob_tbl.get_biggest(),
-            );
+            self.add_file(blob_tbl.id, FileMeta::from_blob_table(blob_tbl));
         }
     }
 
@@ -774,11 +736,19 @@ impl ShardMeta {
             // Although `max_ts` will be updated in initial flush again, still set here to
             // avoid issue in unexpected corner case.
             meta.max_ts = self.max_ts;
+            meta.schema_file_id = self.schema_file_id;
+            meta.schema_file_ver = self.schema_file_ver;
             new_shards.push(meta);
         }
         for new_shard in &mut new_shards {
             for (fid, fm) in &old.files {
                 if new_shard.overlap_table(fm.smallest(), fm.biggest()) {
+                    if fm.get_level() == 0
+                        && new_shard.schema_file_id > 0
+                        && old.unconverted_l0s.contains(fid)
+                    {
+                        new_shard.unconverted_l0s.push(*fid);
+                    }
                     new_shard.files.insert(*fid, fm.clone());
                 }
             }
@@ -786,6 +756,20 @@ impl ShardMeta {
         new_shards
     }
 
+    pub fn apply_columnar_compaction(&mut self, comp: &pb::ColumnarCompaction) {
+        let col_change = comp.get_columnar_change();
+        for col_create in col_change.get_table_creates() {
+            self.files
+                .insert(col_create.get_id(), FileMeta::from_table(col_create));
+        }
+        for col_delete in col_change.get_table_deletes() {
+            self.files.remove(&col_delete.get_id());
+        }
+        self.unconverted_l0s
+            .retain(|unconverted_l0| !comp.row_l0s.contains(unconverted_l0));
+        self.columnar_snap_version = self.columnar_snap_version.max(comp.snap_version);
+    }
+
     pub fn to_change_set(&self) -> pb::ChangeSet {
         let mut cs = new_change_set(self.id, self.ver);
         cs.set_sequence(self.seq);
@@ -797,6 +781,7 @@ impl ShardMeta {
         snap.set_base_version(self.base_version);
         snap.set_data_sequence(self.data_sequence);
         snap.set_max_ts(self.max_ts);
+        snap.set_columnar_snap_version(self.columnar_snap_version);
         for (k, v) in self.files.iter() {
             if v.is_blob_file() {
                 let mut blob = pb::BlobCreate::new();
@@ -804,6 +789,12 @@ impl ShardMeta {
                 blob.set_smallest(v.smallest.to_vec());
                 blob.set_biggest(v.biggest.to_vec());
                 snap.mut_blob_creates().push(blob);
+            } else if v.is_columnar_file() {
+                let mut col_file = pb::TableCreate::new();
+                col_file.set_id(*k);
+                col_file.set_level(v.get_level());
+                col_file.set_columnar_tables(v.columnar_tables);
+                snap.mut_columnar_creates().push(col_file);
             } else if v.get_level() == 0 {
                 let mut l0 = pb::L0Create::new();
                 l0.set_id(*k);
@@ -826,6 +817,7 @@ impl ShardMeta {
             sm.set_version(self.schema_file_ver);
             snap.set_schema_meta(sm);
         }
+        snap.set_unconverted_l0s(self.unconverted_l0s.clone());
         cs.set_snapshot(snap);
         if let Some(parent) = &self.parent {
             cs.set_parent(parent.to_change_set());
@@ -987,6 +979,10 @@ impl ShardMeta {
             ) {
                 self.set_property(DEL_PREFIXES_KEY, &new_del_prefixes);
             }
+            if source.schema_file_ver >= self.schema_file_ver {
+                self.schema_file_id = source.schema_file_id;
+                self.schema_file_ver = source.schema_file_ver;
+            }
         } else {
             info!(
                 "{} clear data of source region on merge, source: {:?}",
diff --git a/components/kvengine/src/options.rs b/components/kvengine/src/options.rs
index e2b10986f..387f5db73 100644
--- a/components/kvengine/src/options.rs
+++ b/components/kvengine/src/options.rs
@@ -13,7 +13,7 @@ use crate::{
         DEFAULT_COMPACTION_REQUEST_VERSION, DEFAULT_COMPACTION_TOMBS_COUNT,
         DEFAULT_COMPACTION_TOMBS_RATIO,
     },
-    table::{blobtable, sstable},
+    table::{blobtable, columnar, sstable},
     *,
 };
 
@@ -37,6 +37,8 @@ pub struct Options {
 
     pub blob_table_build_options: blobtable::builder::BlobTableBuildOptions,
 
+    pub columnar_build_options: columnar::ColumnarTableBuildOptions,
+
     pub remote_compactor_addr: String,
 
     pub recovery_concurrency: usize,
@@ -84,6 +86,7 @@ impl Default for Options {
             num_compactors: 3,
             table_builder_options: Default::default(),
             blob_table_build_options: Default::default(),
+            columnar_build_options: Default::default(),
             remote_compactor_addr: Default::default(),
             recovery_concurrency: Default::default(),
             preparation_concurrency: Default::default(),
diff --git a/components/kvengine/src/prepare.rs b/components/kvengine/src/prepare.rs
index 408b14d65..0ef1ee945 100644
--- a/components/kvengine/src/prepare.rs
+++ b/components/kvengine/src/prepare.rs
@@ -23,7 +23,7 @@ use crate::{
     error::IoContext,
     metrics::ENGINE_LEVEL_WRITE_VEC,
     table::{
-        columnar::schema_file::SchemaFile,
+        columnar::SchemaFile,
         sstable::{InMemFile, LocalFile},
         table::TableExt,
     },
@@ -123,6 +123,12 @@ impl EngineCore {
         if cs.has_update_schema_meta() {
             schema_meta = Some(cs.get_update_schema_meta());
         }
+        if cs.has_columnar_compaction() {
+            let columnar_comp = cs.get_columnar_compaction();
+            for tbl in columnar_comp.get_columnar_change().get_table_creates() {
+                ids.insert(tbl.get_id(), FileMeta::from_table(tbl));
+            }
+        }
         let mut encryption_key = encryption_key;
         if let Some(snap) = snap {
             self.collect_snap_ids(snap, &mut ids);
@@ -219,6 +225,11 @@ impl EngineCore {
                     cs.add_file(id, file, tb, self.cache.clone(), encryption_key.clone())?;
                     continue;
                 }
+            } else if tb.columnar_tables > 0 {
+                if let Ok(file) = self.open_columnar_file(id) {
+                    cs.add_file(id, file, tb, self.cache.clone(), encryption_key.clone())?;
+                    continue;
+                }
             } else if let Ok(file) = self.open_sstable_file(id) {
                 cs.add_file(id, file, tb, self.cache.clone(), encryption_key.clone())?;
                 continue;
@@ -309,7 +320,7 @@ impl EngineCore {
         for (id, tbl) in cs.blob_tables.drain() {
             new_blob_tbl_map.insert(id, tbl);
         }
-
+        let mut new_columnar_levels = data.col_levels.clone();
         // level n
         let mut scf_builders = vec![];
         for cf in 0..NUM_CFS {
@@ -328,6 +339,11 @@ impl EngineCore {
             .into_iter()
             .filter(|(_, tbl)| tbl.get_level() > 0 && !tbl.is_blob_file())
         {
+            if tbl.is_columnar_file() {
+                let col_file = cs.col_files.get(&id).unwrap().clone();
+                new_columnar_levels.add_file(tbl.level as usize, col_file);
+                continue;
+            }
             let sst = cs.ln_tables.get(&id).unwrap();
             let scf = &mut scf_builders.as_mut_slice()[tbl.get_cf() as usize];
             scf.add_table(sst.clone(), tbl.get_level() as usize);
@@ -337,6 +353,7 @@ impl EngineCore {
             let scf = &mut scf_builders.as_mut_slice()[cf];
             scfs[cf] = scf.build();
         }
+        new_columnar_levels.sort();
 
         let new_data = ShardData::new(
             data.range.clone(),
@@ -349,6 +366,7 @@ impl EngineCore {
             data.limiter.clone(),
             data.update_counter + 1,
             data.schema_file.clone(),
+            new_columnar_levels,
         );
         shard.set_data(new_data);
         Ok(())
@@ -413,6 +431,15 @@ impl EngineCore {
         )?)
     }
 
+    fn open_columnar_file(&self, id: u64) -> Result<LocalFile> {
+        let _guard = self.lock_file(id);
+        Ok(LocalFile::open(
+            id,
+            self.local_columnar_file_path(id).as_path(),
+            self.loaded.load(Relaxed),
+        )?)
+    }
+
     pub async fn load_schema_file(&self, id: u64) -> Result<SchemaFile> {
         if let Some(schema_file) = self.schema_files.get(&id) {
             return Ok(schema_file.clone());
@@ -460,6 +487,10 @@ impl EngineCore {
         self.opts.local_dir.join(new_schema_filename(file_id))
     }
 
+    pub(crate) fn local_columnar_file_path(&self, file_id: u64) -> PathBuf {
+        self.opts.local_dir.join(new_columnar_filename(file_id))
+    }
+
     fn tmp_file_path(&self, file_id: u64) -> PathBuf {
         let tmp_id = self
             .tmp_file_id
diff --git a/components/kvengine/src/read.rs b/components/kvengine/src/read.rs
index 919f3761e..a7eb4690b 100644
--- a/components/kvengine/src/read.rs
+++ b/components/kvengine/src/read.rs
@@ -19,6 +19,10 @@ use crate::{
     limiter::RegionLimiter,
     table::{
         blobtable::blobtable::BlobPrefetcher,
+        columnar::{
+            ColumnarMergeReader, ColumnarMvccReader, ColumnarReader, ColumnarRowTableReader,
+            ColumnarTableReader, Schema,
+        },
         memtable::{Hint, WriteBatch},
         sstable::BlockCacheKey,
         table, InnerKey, SkipOpTxnFileIterator, TableExt, TxnFile, TxnFileIterator,
@@ -179,6 +183,7 @@ pub struct SnapAccessCore {
     _base_version: u64,
     meta_seq: u64,
     write_sequence: u64,
+    columnar_snap_version: u64,
     data: ShardData,
     get_hint: Mutex<Hint>,
     blob_table_prefetch_size: usize,
@@ -191,10 +196,12 @@ impl SnapAccessCore {
         let base_version = shard.get_base_version();
         let meta_seq = shard.get_meta_sequence();
         let write_sequence = shard.get_write_sequence();
+        let columnar_snap_version = shard.get_columnar_snap_version();
         let data = shard.get_data();
         Self {
             tag: shard.tag(),
             write_sequence,
+            columnar_snap_version,
             meta_seq,
             _base_version: base_version,
             managed_ts: 0,
@@ -958,6 +965,48 @@ impl SnapAccessCore {
     pub fn get_limiter(&self) -> &RegionLimiter {
         &self.data.limiter
     }
+
+    pub fn new_columnar_mvcc_reader(
+        &self,
+        schema: &Schema,
+        read_ts: u64,
+    ) -> Option<ColumnarMvccReader> {
+        if self.columnar_snap_version == 0 {
+            return None;
+        }
+        let mut readers: Vec<Box<dyn ColumnarReader>> = vec![];
+        for mem in &self.data.mem_tbls {
+            let skl = mem.get_cf(WRITE_CF);
+            if !skl.is_empty() {
+                let iter = skl.new_iterator(false);
+                let row_reader =
+                    ColumnarRowTableReader::new(self.data.keyspace_id, schema.clone(), iter, false);
+                readers.push(Box::new(row_reader));
+            }
+        }
+        for l0 in &self.data.col_levels.unconverted_l0s {
+            if let Some(l0_write) = l0.get_cf(WRITE_CF) {
+                let iter = l0_write.new_iterator(false, true);
+                let row_reader =
+                    ColumnarRowTableReader::new(self.data.keyspace_id, schema.clone(), iter, false);
+                readers.push(Box::new(row_reader));
+            }
+        }
+        for columnar_level in &self.data.col_levels.levels {
+            for col_file in &columnar_level.files {
+                let col_reader = ColumnarTableReader::new(
+                    col_file,
+                    schema.table_id,
+                    schema.columns.clone(),
+                    false,
+                );
+                readers.push(Box::new(col_reader));
+            }
+        }
+        let merged_reader = ColumnarMergeReader::new(schema.clone(), readers);
+        let mvcc_reader = ColumnarMvccReader::new(Box::new(merged_reader), schema, read_ts);
+        Some(mvcc_reader)
+    }
 }
 
 pub struct Iterator {
@@ -1230,7 +1279,7 @@ mod tests {
             &master_key,
         );
 
-        let (l0s, blob_tbls, scfs, lock_txn_files) =
+        let (l0s, blob_tbls, scfs, lock_txn_files, col_lvls) =
             create_snapshot_tables(cs.get_snapshot(), &cs, false);
         let data = ShardData::new(
             shard.range.clone(),
@@ -1243,6 +1292,7 @@ mod tests {
             RegionLimiter::dummy(),
             NEW_DATA_UPDATE_COUNTER,
             cs.schema_file.clone(),
+            col_lvls,
         );
         shard.set_data(data);
         let snap = shard.new_snap_access();
diff --git a/components/kvengine/src/shard.rs b/components/kvengine/src/shard.rs
index f3c1e4f2f..22558075e 100644
--- a/components/kvengine/src/shard.rs
+++ b/components/kvengine/src/shard.rs
@@ -27,7 +27,7 @@ use crate::{
     table::{
         self,
         blobtable::blobtable::BlobTable,
-        columnar::schema_file::SchemaFile,
+        columnar::{ColumnarFile, SchemaFile},
         get_tables_in_range,
         memtable::{self, CfTable, WriteBatch},
         search,
@@ -93,6 +93,13 @@ pub struct Shard {
     // write_sequence is the raft log index of the applied write batch.
     pub(crate) write_sequence: AtomicU64,
 
+    // snap_version is the latest L0 table's version, equals to:
+    //     ShardMeta.data_sequence + ShardMeta.base_version
+    pub(crate) snap_version: AtomicU64,
+    // col_snap_version is the latest L0 table's version that has been compacted to
+    // columnar file. It is used to determine whether the columnar file is up-to-date.
+    pub(crate) col_snap_version: AtomicU64,
+
     pub(crate) compaction_priority: RwLock<Option<CompactionPriority>>,
 
     pub(crate) encryption_key: Option<EncryptionKey>,
@@ -191,6 +198,8 @@ impl Shard {
             entries_write_cf: Default::default(),
             meta_seq: Default::default(),
             write_sequence: Default::default(),
+            snap_version: Default::default(),
+            col_snap_version: Default::default(),
             compaction_priority: RwLock::new(None),
             encryption_key,
         };
@@ -246,6 +255,12 @@ impl Shard {
         shard.meta_seq.store(cs.sequence, Release);
         shard.write_sequence.store(snap.data_sequence, Release);
         shard
+            .snap_version
+            .store(snap.base_version + snap.data_sequence, Release);
+        shard
+            .col_snap_version
+            .store(cs.get_snapshot().get_columnar_snap_version(), Release);
+        shard
     }
 
     pub async fn from_change_set(
@@ -355,7 +370,7 @@ impl Shard {
         // TODO: load lock_txn_files
 
         let mut shard = Shard::new_for_ingest(0, &cs, Arc::new(Options::default()), master_key);
-        let (l0s, blob_tbls, scfs, lock_txn_files) =
+        let (l0s, blob_tbls, scfs, lock_txn_files, col_levels) =
             create_snapshot_tables(cs.get_snapshot(), &cs, ignore_lock);
         let data = ShardData::new(
             shard.range.clone(),
@@ -368,6 +383,7 @@ impl Shard {
             RegionLimiter::new((&shard.opt.flow_control).into()), // Note: limiter is disabled here
             NEW_DATA_UPDATE_COUNTER,
             cs.schema_file.clone(),
+            col_levels,
         );
         shard.id = cs.shard_id;
         shard.set_data(data);
@@ -675,6 +691,14 @@ impl Shard {
         self.write_sequence.load(Ordering::Acquire)
     }
 
+    pub fn get_snap_version(&self) -> u64 {
+        self.snap_version.load(Ordering::Acquire)
+    }
+
+    pub fn get_columnar_snap_version(&self) -> u64 {
+        self.col_snap_version.load(Ordering::Acquire)
+    }
+
     pub fn get_meta_sequence(&self) -> u64 {
         self.meta_seq.load(Ordering::Acquire)
     }
@@ -783,6 +807,11 @@ impl Shard {
                 return;
             }
         }
+        if data.schema_file.is_some() && !data.col_levels.unconverted_l0s.is_empty() {
+            let mut lock = self.compaction_priority.write().unwrap();
+            *lock = Some(CompactionPriority::L0ToColumnar);
+            return;
+        }
         let mut score = 0.0;
         let mut cf_with_highest_score = -1;
         let mut level_with_highest_score = 0;
@@ -985,6 +1014,7 @@ impl Shard {
             shard_data.limiter.clone(),
             shard_data.update_counter + 1,
             shard_data.schema_file.clone(),
+            shard_data.col_levels.clone(),
         );
         self.set_data(new_data);
     }
@@ -1068,6 +1098,7 @@ impl ShardData {
             limiter,
             INITIAL_UPDATE_COUNTER,
             None,
+            ColumnarLevels::new(),
         )
     }
 
@@ -1082,6 +1113,7 @@ impl ShardData {
         limiter: RegionLimiter,
         update_counter: u64,
         schema_file: Option<SchemaFile>,
+        col_levels: ColumnarLevels,
     ) -> Self {
         assert!(!mem_tbls.is_empty());
 
@@ -1097,6 +1129,7 @@ impl ShardData {
                 limiter,
                 update_counter,
                 schema_file,
+                col_levels,
             }),
         }
     }
@@ -1114,6 +1147,7 @@ pub(crate) struct ShardDataCore {
     pub limiter: RegionLimiter,
     pub update_counter: u64,
     pub(crate) schema_file: Option<SchemaFile>,
+    pub(crate) col_levels: ColumnarLevels,
 }
 
 impl Deref for ShardDataCore {
@@ -2115,6 +2149,65 @@ impl fmt::Debug for ShardRange {
     }
 }
 
+#[derive(Clone, Default)]
+pub(crate) struct ColumnarLevel {
+    pub(crate) level: usize,
+    pub(crate) files: Vec<ColumnarFile>,
+}
+
+impl ColumnarLevel {
+    pub(crate) fn new(level: usize) -> Self {
+        Self {
+            level,
+            files: vec![],
+        }
+    }
+
+    pub(crate) fn sort(&mut self) {
+        if self.level < 2 {
+            self.files.sort_by(|a, b| {
+                let a_l0_version = a.get_l0_version().unwrap();
+                let b_l0_version = b.get_l0_version().unwrap();
+                b_l0_version.cmp(&a_l0_version)
+            })
+        } else {
+            self.files
+                .sort_by(|a, b| a.get_smallest().cmp(&b.get_smallest()))
+        }
+    }
+}
+
+#[derive(Clone)]
+pub(crate) struct ColumnarLevels {
+    pub(crate) unconverted_l0s: Vec<L0Table>,
+    pub(crate) levels: Vec<ColumnarLevel>,
+}
+
+impl ColumnarLevels {
+    pub(crate) fn new() -> Self {
+        Self {
+            unconverted_l0s: vec![],
+            levels: vec![
+                ColumnarLevel::new(0),
+                ColumnarLevel::new(1),
+                ColumnarLevel::new(2),
+            ],
+        }
+    }
+
+    pub(crate) fn sort(&mut self) {
+        self.levels.iter_mut().for_each(|l| l.sort());
+    }
+
+    pub(crate) fn add_file(&mut self, level: usize, file: ColumnarFile) {
+        self.levels[level].files.push(file);
+    }
+
+    pub(crate) fn retain(&mut self, f: impl Fn(&ColumnarFile) -> bool) {
+        self.levels.iter_mut().for_each(|l| l.files.retain(&f));
+    }
+}
+
 #[cfg(test)]
 mod tests {
     use super::*;
diff --git a/components/kvengine/src/split.rs b/components/kvengine/src/split.rs
index b5a3785af..6a14338e0 100644
--- a/components/kvengine/src/split.rs
+++ b/components/kvengine/src/split.rs
@@ -3,6 +3,7 @@
 use std::{
     cmp::{max, min},
     collections::HashMap,
+    iter::Iterator,
     sync::{
         atomic::{Ordering, Ordering::Release},
         Arc,
@@ -14,6 +15,7 @@ use api_version::{
     ApiV2, KeyMode, KvFormat,
 };
 use bytes::{Buf, Bytes};
+use collections::HashSet;
 use dashmap::mapref::entry::Entry;
 use kvenginepb as pb;
 use slog_global::info;
@@ -96,6 +98,7 @@ impl Engine {
                 store_u64(&new_shard.meta_seq, initial_seq);
                 store_u64(&new_shard.write_sequence, initial_seq);
             }
+            store_u64(&new_shard.snap_version, old_shard.get_snap_version());
             if !old_del_prefixes.is_empty() {
                 // We need to use the old shard's DEL_PREFIXES_KEY to overwrite the new shard's
                 // DEL_PREFIXES_KEY. because the destroy_range compaction may have not
@@ -108,12 +111,22 @@ impl Engine {
             }
             new_shards.push(Arc::new(new_shard));
         }
+        let unconverted_l0s: HashSet<u64> = old_data
+            .col_levels
+            .unconverted_l0s
+            .iter()
+            .map(|l0| l0.id())
+            .collect();
         for new_shard in &new_shards {
             let new_mem_tbls = new_shard.split_mem_tables(&old_data.mem_tbls);
             let mut new_l0s = vec![];
+            let mut new_unconverted_l0s = vec![];
             for l0 in &old_data.l0_tbls {
                 if new_shard.overlap_table(l0.smallest(), l0.biggest()) {
                     new_l0s.push(l0.clone());
+                    if unconverted_l0s.contains(&l0.id()) {
+                        new_unconverted_l0s.push(l0.clone());
+                    }
                 }
             }
             let mut new_blob_tbl_map = HashMap::new();
@@ -137,6 +150,16 @@ impl Engine {
                 }
             }
             let schema_file = old_data.schema_file.clone();
+            let mut new_col_levels = ColumnarLevels::new();
+            new_col_levels.unconverted_l0s = new_unconverted_l0s;
+            for col_level in &old_data.col_levels.levels {
+                let new_col_level = &mut new_col_levels.levels[col_level.level];
+                for col_file in &col_level.files {
+                    if new_shard.overlap_table(col_file.get_smallest(), col_file.get_biggest()) {
+                        new_col_level.files.push(col_file.clone());
+                    }
+                }
+            }
             let new_data = ShardData::new(
                 new_shard.range.clone(),
                 new_mem_tbls,
@@ -148,6 +171,7 @@ impl Engine {
                 RegionLimiter::new_from(&old_data.limiter),
                 NEW_DATA_UPDATE_COUNTER,
                 schema_file,
+                new_col_levels,
             );
             new_shard.set_data(new_data);
         }
@@ -413,6 +437,12 @@ impl Engine {
                 old_shard.tag(),
                 source
             );
+            let mut columnar_levels = old_data.col_levels.clone();
+            for columnar_create in source_snap.get_columnar_creates() {
+                let col = source.col_files.get(&columnar_create.id).unwrap().clone();
+                columnar_levels.add_file(columnar_create.level as usize, col);
+            }
+            columnar_levels.sort();
             ShardData::new(
                 new_shard.range.clone(),
                 mem_tbls,
@@ -424,6 +454,7 @@ impl Engine {
                 old_data.limiter.clone(),
                 old_data.update_counter + 1,
                 old_data.schema_file.clone(),
+                columnar_levels,
             )
         } else {
             info!(
@@ -445,6 +476,7 @@ impl Engine {
                 old_data.limiter.clone(),
                 old_data.update_counter + 1,
                 old_data.schema_file.clone(),
+                old_data.col_levels.clone(),
             )
         };
         new_shard.set_data(data);
diff --git a/components/kvengine/src/table/columnar/builder.rs b/components/kvengine/src/table/columnar/builder.rs
index 17969c1a7..32aef6400 100644
--- a/components/kvengine/src/table/columnar/builder.rs
+++ b/components/kvengine/src/table/columnar/builder.rs
@@ -68,6 +68,9 @@ pub struct ColumnarFileBuilder {
     pub file_id: u64,
     snap_version: Option<u64>,
     tables: Vec<ColumnarTableBuilder>,
+    pub(crate) estimated_size: usize,
+    pub(crate) smallest: Vec<u8>,
+    pub(crate) biggest: Vec<u8>,
 }
 
 #[derive(Debug)]
@@ -144,15 +147,22 @@ impl ColumnarFileBuilder {
         ColumnarFileBuilder {
             file_id,
             snap_version,
+            estimated_size: 0,
             tables: vec![],
+            smallest: vec![],
+            biggest: vec![],
         }
     }
 
     pub fn add_table(&mut self, table: ColumnarTableBuilder) {
+        self.estimated_size += table.get_estimated_size();
         self.tables.push(table);
     }
 
     pub fn build(&mut self) -> Vec<u8> {
+        if self.tables.is_empty() {
+            return vec![];
+        }
         self.tables
             .sort_by(|a, b| a.schema.table_id.cmp(&b.schema.table_id));
         let mut tables_offsets = vec![];
@@ -169,8 +179,18 @@ impl ColumnarFileBuilder {
         }
         let mut property_buf = vec![];
         let (smallest, biggest) = self.build_smallest_biggest();
-        add_property(&mut property_buf, PROP_KEY_SMALLEST.as_bytes(), &smallest);
-        add_property(&mut property_buf, PROP_KEY_BIGGEST.as_bytes(), &biggest);
+        self.smallest = smallest;
+        self.biggest = biggest;
+        add_property(
+            &mut property_buf,
+            PROP_KEY_SMALLEST.as_bytes(),
+            &self.smallest,
+        );
+        add_property(
+            &mut property_buf,
+            PROP_KEY_BIGGEST.as_bytes(),
+            &self.biggest,
+        );
         add_property(
             &mut property_buf,
             PROP_KEY_MAX_VERSION.as_bytes(),
@@ -230,6 +250,16 @@ impl ColumnarFileBuilder {
         };
         (smallest_key, biggest_key)
     }
+
+    pub(crate) fn num_tables(&self) -> usize {
+        self.tables.len()
+    }
+
+    pub(crate) fn reset(&mut self, id: u64) {
+        self.file_id = id;
+        self.estimated_size = 0;
+        self.tables.clear();
+    }
 }
 
 pub struct ColumnarTableBuilder {
@@ -282,12 +312,10 @@ impl ColumnarTableBuilder {
         }
     }
 
-    pub(crate) fn add_block(&mut self, block: &Block) {
-        self.append_block(block, 0, block.handles.length());
-    }
-
-    fn append_block(&mut self, block: &Block, start_offset: usize, end_offset: usize) {
-        self.handle_builder
+    pub(crate) fn append_block(&mut self, block: &Block, start_offset: usize) -> usize {
+        let mut end_offset = block.length();
+        end_offset = self
+            .handle_builder
             .append_handle(&block.handles, start_offset, end_offset);
         self.version_builder
             .append(&block.versions, start_offset, end_offset);
@@ -301,6 +329,7 @@ impl ColumnarTableBuilder {
         for i in start_offset..end_offset {
             self.max_version = max(self.max_version, block.versions.get_version(i))
         }
+        end_offset
     }
 
     fn finish_table(&mut self) {
@@ -338,6 +367,9 @@ impl ColumnarTableBuilder {
 
     pub(crate) fn compute_size(&self) -> DataSizeTuple {
         let mut total_size = DataSizeTuple::default();
+        if self.handle_builder.row_count == 0 {
+            return total_size;
+        }
         let handle_col_size = self.handle_builder.compute_size();
         total_size.add(handle_col_size);
         let version_col_size = self.version_builder.compute_size();
@@ -405,6 +437,16 @@ impl ColumnarTableBuilder {
         output_buf.put_u32_le(self.properties.len() as u32);
         output_buf.extend_from_slice(&self.properties);
     }
+
+    pub(crate) fn get_estimated_size(&self) -> usize {
+        let mut estimated_size = self.handle_builder.estimated_size
+            + self.version_builder.estimated_size
+            + self.txn_id_builder.estimated_size;
+        for cb in &self.column_builders {
+            estimated_size += cb.estimated_size;
+        }
+        estimated_size
+    }
 }
 
 pub struct ColumnarColumnBuilder {
@@ -489,7 +531,7 @@ impl ColumnarColumnBuilder {
         input: &ColumnBuffer,
         row_offset: usize,
         row_end_off: usize,
-    ) {
+    ) -> usize {
         debug_assert!(self.is_handle);
         for i in row_offset..row_end_off {
             let handle = input.get_not_null_value(i);
@@ -498,11 +540,13 @@ impl ColumnarColumnBuilder {
                 let last_handle = self.pack_buffer.get_not_null_value(current_length - 1);
                 if last_handle != handle {
                     self.finish_pack();
+                    return i;
                 }
             }
             self.pack_buffer.push_value(handle);
             self.row_count += 1;
         }
+        row_end_off
     }
 
     fn finish_pack(&mut self) {
diff --git a/components/kvengine/src/table/columnar/columnar.rs b/components/kvengine/src/table/columnar/columnar.rs
index 02e1b5827..f952288e3 100644
--- a/components/kvengine/src/table/columnar/columnar.rs
+++ b/components/kvengine/src/table/columnar/columnar.rs
@@ -327,6 +327,7 @@ impl PackOffsets {
     }
 }
 
+#[derive(Clone)]
 pub struct ColumnarFile {
     core: Arc<ColumnarFileCore>,
 }
@@ -424,7 +425,7 @@ struct ColumnarFileCore {
     tables: HashMap<i64, Arc<TableMeta>>,
 }
 
-pub(crate) struct ColumnBuffer {
+pub struct ColumnBuffer {
     pub(crate) col_id: i32,
     pub(crate) nullable: bool,
     pub(crate) fixed_size: usize,
@@ -481,7 +482,7 @@ impl ColumnBuffer {
         self.length()
     }
 
-    pub fn reset(&mut self) {
+    pub(crate) fn reset(&mut self) {
         if self.fixed_size == 0 {
             self.offsets.truncate(1);
         }
@@ -524,8 +525,7 @@ impl ColumnBuffer {
         }
     }
 
-    #[allow(dead_code)]
-    pub(crate) fn get_nullable_value(&self, idx: usize) -> Option<&[u8]> {
+    pub fn get_nullable_value(&self, idx: usize) -> Option<&[u8]> {
         debug_assert!(self.nullable);
         if self.nulls[idx] == 1 {
             return None;
@@ -542,7 +542,7 @@ impl ColumnBuffer {
     }
 
     #[inline]
-    pub(crate) fn get_not_null_value(&self, idx: usize) -> &[u8] {
+    pub fn get_not_null_value(&self, idx: usize) -> &[u8] {
         debug_assert!(
             !self.nullable || self.nulls[idx] == 0,
             "id: {}, nulls: {:?}, idx {}",
@@ -562,19 +562,19 @@ impl ColumnBuffer {
     }
 
     #[inline]
-    pub(crate) fn get_int_handle_value(&self, idx: usize) -> i64 {
+    pub fn get_int_handle_value(&self, idx: usize) -> i64 {
         debug_assert!(self.fixed_size == 8);
         let start = idx * self.fixed_size;
         let end = (idx + 1) * self.fixed_size;
         (&self.data_buf[start..end]).get_i64_le()
     }
 
-    pub(crate) fn get_version(&self, idx: usize) -> u64 {
+    pub fn get_version(&self, idx: usize) -> u64 {
         debug_assert!(self.col_id == VERSION_COL_ID || self.col_id == TXN_ID_COL_ID);
         (&self.data_buf[idx * 8..]).get_u64_le()
     }
 
-    pub(crate) fn is_null(&self, idx: usize) -> bool {
+    pub fn is_null(&self, idx: usize) -> bool {
         debug_assert!(self.nullable);
         self.nulls[idx] == 1
     }
@@ -675,7 +675,7 @@ pub struct Block {
 }
 
 impl Block {
-    pub(crate) fn new(schema: &Schema) -> Self {
+    pub fn new(schema: &Schema) -> Self {
         let handles = ColumnBuffer::new_from_col_info(&schema.handle_column);
         let versions = ColumnBuffer::new_from_col_info(&schema.version_column);
         let txn_ids = schema
@@ -726,6 +726,22 @@ impl Block {
             col.append(other_col, row_offset, row_end_offset);
         }
     }
+
+    pub fn length(&self) -> usize {
+        self.handles.length()
+    }
+
+    pub fn get_handle_buf(&self) -> &ColumnBuffer {
+        &self.handles
+    }
+
+    pub fn get_version_buf(&self) -> &ColumnBuffer {
+        &self.versions
+    }
+
+    pub fn get_columns(&self) -> &[ColumnBuffer] {
+        &self.columns
+    }
 }
 
 pub(crate) fn get_fixed_size(col_info: &ColumnInfo) -> usize {
diff --git a/components/kvengine/src/table/columnar/mod.rs b/components/kvengine/src/table/columnar/mod.rs
index c5e1aff48..e4f8562b5 100644
--- a/components/kvengine/src/table/columnar/mod.rs
+++ b/components/kvengine/src/table/columnar/mod.rs
@@ -1,6 +1,11 @@
 // Copyright 2024 TiKV Project Authors. Licensed under Apache-2.0.
 
-pub mod builder;
-pub mod columnar;
-pub mod reader;
-pub mod schema_file;
+mod builder;
+mod columnar;
+mod reader;
+mod schema_file;
+
+pub use builder::*;
+pub use columnar::*;
+pub use reader::*;
+pub use schema_file::*;
diff --git a/components/kvengine/src/table/columnar/reader.rs b/components/kvengine/src/table/columnar/reader.rs
index 060409102..f06356c8f 100644
--- a/components/kvengine/src/table/columnar/reader.rs
+++ b/components/kvengine/src/table/columnar/reader.rs
@@ -362,7 +362,7 @@ impl ColumnarMvccReader {
             for i in 0..block.handles.length() {
                 let handle = block.handles.get_int_handle_value(i);
                 let version = block.versions.get_version(i);
-                if version > self.read_ts || handle == prev_handle {
+                if version > self.read_ts || (i > 0 && handle == prev_handle) {
                     self.finish_range(i);
                     continue;
                 }
@@ -613,7 +613,9 @@ impl ColumnarReader for ColumnarMergeReader {
             reader.seek(handle)?;
         }
         self.heap.retain(|r| r.valid());
-        self.init_heap();
+        if !self.heap.is_empty() {
+            self.init_heap();
+        }
         Ok(())
     }
 
@@ -805,7 +807,7 @@ impl ColumnarReader for ColumnarRowTableReader {
     }
 
     fn seek(&mut self, handle: &[u8]) -> table::Result<()> {
-        let row_key = if self.is_int_handle {
+        let row_key = if self.is_int_handle && !handle.is_empty() {
             encode_row_key(self.schema.table_id, (&handle[..]).get_i64_le())
         } else {
             encode_common_handle_for_test(self.schema.table_id, handle)
@@ -1006,16 +1008,20 @@ mod tests {
         opts.pack_max_size = 256;
         let mut table_builder = ColumnarTableBuilder::new(schema.clone(), opts, true);
         row_tbl_reader.seek(&ref_rows[0].handle).unwrap();
-        let mut read_rows = 0;
-        while read_rows < ref_rows.len() {
-            block.reset();
-            let limit = rng.gen_range(2..10);
-            let read = row_tbl_reader.read(&mut block, limit).unwrap();
-            if read == 0 {
-                break;
+        let mut append_rows = 0;
+        let mut block_off = 0;
+        while append_rows < ref_rows.len() {
+            if block_off == block.length() {
+                block_off = 0;
+                block.reset();
+                let limit = rng.gen_range(2..10);
+                let read = row_tbl_reader.read(&mut block, limit).unwrap();
+                if read == 0 {
+                    break;
+                }
             }
-            read_rows += read;
-            table_builder.add_block(&block);
+            block_off = table_builder.append_block(&block, block_off);
+            append_rows += block.length() - block_off;
         }
         let mut file_builder = ColumnarFileBuilder::new(file_id, None);
         file_builder.add_table(table_builder);
diff --git a/components/kvengine/src/table/columnar/schema_file.rs b/components/kvengine/src/table/columnar/schema_file.rs
index d2b5762ef..e20136db9 100644
--- a/components/kvengine/src/table/columnar/schema_file.rs
+++ b/components/kvengine/src/table/columnar/schema_file.rs
@@ -1,6 +1,6 @@
 // Copyright 2024 TiKV Project Authors. Licensed under Apache-2.0.
 
-use std::{collections::HashMap, sync::Arc};
+use std::{collections::HashMap, ops::Deref, sync::Arc};
 
 use api_version::api_v2::KEYSPACE_PREFIX_LEN;
 use bytes::{Buf, BufMut};
@@ -17,7 +17,7 @@ use crate::table::{
         columnar::Schema,
     },
     sstable::{File, NO_COMPRESSION},
-    ChecksumType,
+    ChecksumType, InnerKey,
 };
 
 pub const SCHEMA_FILE_MAGIC: u32 = 0x5353484D;
@@ -200,6 +200,17 @@ impl SchemaFile {
         false
     }
 
+    pub fn overlap_tables(&self, smallest: InnerKey<'_>, biggest: InnerKey<'_>) -> Vec<i64> {
+        let start_table_id = decode_table_id(smallest.deref()).unwrap_or(0);
+        let end_table_id = decode_table_id(biggest.deref()).unwrap_or(i64::MAX);
+        self.core
+            .tables
+            .keys()
+            .filter(|&&tid| start_table_id <= tid && tid <= end_table_id)
+            .copied()
+            .collect()
+    }
+
     pub fn contains(&self, others: &[Schema]) -> bool {
         for schema in others {
             if self.get_table(schema.table_id).is_none()
diff --git a/components/kvengine/src/tests.rs b/components/kvengine/src/tests.rs
index c9f86411b..f0e3ab7ad 100644
--- a/components/kvengine/src/tests.rs
+++ b/components/kvengine/src/tests.rs
@@ -618,6 +618,7 @@ fn test_lost_tombstone_issue() {
         RegionLimiter::dummy(),
         TEST_ENGINE_NEW_DATA_UPDATE_COUNTER,
         None,
+        ColumnarLevels::new(),
     );
     shard.set_data(data);
     let pri = CompactionPriority::L1Plus {
@@ -676,6 +677,7 @@ fn test_read_iterator_all_versions() {
         RegionLimiter::dummy(),
         TEST_ENGINE_NEW_DATA_UPDATE_COUNTER,
         None,
+        ColumnarLevels::new(),
     );
     shard.set_data(data);
 
@@ -751,6 +753,7 @@ fn test_level_overlapping_tables_impl(enable_inner_key_off: bool) {
         RegionLimiter::dummy(),
         TEST_ENGINE_NEW_DATA_UPDATE_COUNTER,
         None,
+        ColumnarLevels::new(),
     );
 
     let cf0 = data.get_cf(0);
@@ -909,6 +912,7 @@ fn test_get_suggest_split_key_impl(enable_inner_key_off: bool) {
             RegionLimiter::dummy(),
             TEST_ENGINE_NEW_DATA_UPDATE_COUNTER,
             None,
+            ColumnarLevels::new(),
         );
         shard.set_data_opt(data, false);
 
@@ -1096,6 +1100,7 @@ fn test_get_evenly_split_keys_impl(enable_inner_key_off: bool) {
             RegionLimiter::dummy(),
             TEST_ENGINE_NEW_DATA_UPDATE_COUNTER,
             None,
+            ColumnarLevels::new(),
         );
         shard.set_data_opt(data, false);
 
@@ -1161,6 +1166,7 @@ fn test_refresh_stats() {
         RegionLimiter::dummy(),
         TEST_ENGINE_NEW_DATA_UPDATE_COUNTER,
         None,
+        ColumnarLevels::new(),
     );
     shard.set_data(data);
     shard.refresh_states();
diff --git a/components/kvengine/src/write.rs b/components/kvengine/src/write.rs
index 4c12177c0..765f165fc 100644
--- a/components/kvengine/src/write.rs
+++ b/components/kvengine/src/write.rs
@@ -142,6 +142,7 @@ impl Engine {
             data.limiter.clone(),
             data.update_counter + 1,
             data.schema_file.clone(),
+            data.col_levels.clone(),
         );
         new_data.refresh_for_limiter(&shard.tag());
         shard.set_data(new_data);
@@ -305,6 +306,7 @@ impl Engine {
             old_data.limiter.clone(),
             old_data.update_counter + 1,
             old_data.schema_file.clone(),
+            old_data.col_levels.clone(),
         );
         shard.set_data(data);
         is_commit
diff --git a/components/kvenginepb/src/changeset.proto b/components/kvenginepb/src/changeset.proto
index c84d37d8a..a937ed83d 100644
--- a/components/kvenginepb/src/changeset.proto
+++ b/components/kvenginepb/src/changeset.proto
@@ -68,6 +68,7 @@ message SchemaMeta {
 message ColumnarCompaction {
   TableChange columnar_change = 1;
   uint64 snap_version = 2;
+  repeated uint64 row_l0s = 3;
 }
 
 message Flush {
@@ -93,6 +94,7 @@ message Snapshot {
   repeated TableCreate columnarCreates = 12;
   SchemaMeta schema_meta = 13;
   uint64 columnar_snap_version = 14;
+  repeated uint64 unconverted_l0s = 15;
 }
 
 message L0Create {
diff --git a/components/kvenginepb/src/changeset.rs b/components/kvenginepb/src/changeset.rs
index ba265c781..9883a3170 100644
--- a/components/kvenginepb/src/changeset.rs
+++ b/components/kvenginepb/src/changeset.rs
@@ -2290,6 +2290,7 @@ pub struct ColumnarCompaction {
     // message fields
     pub columnar_change: ::protobuf::SingularPtrField<TableChange>,
     pub snap_version: u64,
+    pub row_l0s: ::std::vec::Vec<u64>,
     // special fields
     pub unknown_fields: ::protobuf::UnknownFields,
     pub cached_size: ::protobuf::CachedSize,
@@ -2353,6 +2354,31 @@ impl ColumnarCompaction {
     pub fn set_snap_version(&mut self, v: u64) {
         self.snap_version = v;
     }
+
+    // repeated uint64 row_l0s = 3;
+
+
+    pub fn get_row_l0s(&self) -> &[u64] {
+        &self.row_l0s
+    }
+    pub fn clear_row_l0s(&mut self) {
+        self.row_l0s.clear();
+    }
+
+    // Param is passed by value, moved
+    pub fn set_row_l0s(&mut self, v: ::std::vec::Vec<u64>) {
+        self.row_l0s = v;
+    }
+
+    // Mutable pointer to the field.
+    pub fn mut_row_l0s(&mut self) -> &mut ::std::vec::Vec<u64> {
+        &mut self.row_l0s
+    }
+
+    // Take field
+    pub fn take_row_l0s(&mut self) -> ::std::vec::Vec<u64> {
+        ::std::mem::replace(&mut self.row_l0s, ::std::vec::Vec::new())
+    }
 }
 
 impl ::protobuf::Message for ColumnarCompaction {
@@ -2379,6 +2405,9 @@ impl ::protobuf::Message for ColumnarCompaction {
                     let tmp = is.read_uint64()?;
                     self.snap_version = tmp;
                 },
+                3 => {
+                    ::protobuf::rt::read_repeated_uint64_into(wire_type, is, &mut self.row_l0s)?;
+                },
                 _ => {
                     ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                 },
@@ -2398,6 +2427,9 @@ impl ::protobuf::Message for ColumnarCompaction {
         if self.snap_version != 0 {
             my_size += ::protobuf::rt::value_size(2, self.snap_version, ::protobuf::wire_format::WireTypeVarint);
         }
+        for value in &self.row_l0s {
+            my_size += ::protobuf::rt::value_size(3, *value, ::protobuf::wire_format::WireTypeVarint);
+        };
         my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
         self.cached_size.set(my_size);
         my_size
@@ -2412,6 +2444,9 @@ impl ::protobuf::Message for ColumnarCompaction {
         if self.snap_version != 0 {
             os.write_uint64(2, self.snap_version)?;
         }
+        for v in &self.row_l0s {
+            os.write_uint64(3, *v)?;
+        };
         os.write_unknown_fields(self.get_unknown_fields())?;
         ::std::result::Result::Ok(())
     }
@@ -2464,6 +2499,11 @@ impl ::protobuf::Message for ColumnarCompaction {
                     |m: &ColumnarCompaction| { &m.snap_version },
                     |m: &mut ColumnarCompaction| { &mut m.snap_version },
                 ));
+                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
+                    "row_l0s",
+                    |m: &ColumnarCompaction| { &m.row_l0s },
+                    |m: &mut ColumnarCompaction| { &mut m.row_l0s },
+                ));
                 ::protobuf::reflect::MessageDescriptor::new::<ColumnarCompaction>(
                     "ColumnarCompaction",
                     fields,
@@ -2488,6 +2528,7 @@ impl ::protobuf::Clear for ColumnarCompaction {
     fn clear(&mut self) {
         self.columnar_change.clear();
         self.snap_version = 0;
+        self.row_l0s.clear();
         self.unknown_fields.clear();
     }
 }
@@ -2499,6 +2540,7 @@ impl ::protobuf::PbPrint for ColumnarCompaction {
         let old_len = buf.len();
         ::protobuf::PbPrint::fmt(&self.columnar_change, "columnar_change", buf);
         ::protobuf::PbPrint::fmt(&self.snap_version, "snap_version", buf);
+        ::protobuf::PbPrint::fmt(&self.row_l0s, "row_l0s", buf);
         if old_len < buf.len() {
           buf.push(' ');
         }
@@ -2511,6 +2553,7 @@ impl ::std::fmt::Debug for ColumnarCompaction {
         let mut s = String::new();
         ::protobuf::PbPrint::fmt(&self.columnar_change, "columnar_change", &mut s);
         ::protobuf::PbPrint::fmt(&self.snap_version, "snap_version", &mut s);
+        ::protobuf::PbPrint::fmt(&self.row_l0s, "row_l0s", &mut s);
         write!(f, "{}", s)
     }
 }
@@ -2920,6 +2963,7 @@ pub struct Snapshot {
     pub columnar_creates: ::protobuf::RepeatedField<TableCreate>,
     pub schema_meta: ::protobuf::SingularPtrField<SchemaMeta>,
     pub columnar_snap_version: u64,
+    pub unconverted_l0s: ::std::vec::Vec<u64>,
     // special fields
     pub unknown_fields: ::protobuf::UnknownFields,
     pub cached_size: ::protobuf::CachedSize,
@@ -3228,6 +3272,31 @@ impl Snapshot {
     pub fn set_columnar_snap_version(&mut self, v: u64) {
         self.columnar_snap_version = v;
     }
+
+    // repeated uint64 unconverted_l0s = 15;
+
+
+    pub fn get_unconverted_l0s(&self) -> &[u64] {
+        &self.unconverted_l0s
+    }
+    pub fn clear_unconverted_l0s(&mut self) {
+        self.unconverted_l0s.clear();
+    }
+
+    // Param is passed by value, moved
+    pub fn set_unconverted_l0s(&mut self, v: ::std::vec::Vec<u64>) {
+        self.unconverted_l0s = v;
+    }
+
+    // Mutable pointer to the field.
+    pub fn mut_unconverted_l0s(&mut self) -> &mut ::std::vec::Vec<u64> {
+        &mut self.unconverted_l0s
+    }
+
+    // Take field
+    pub fn take_unconverted_l0s(&mut self) -> ::std::vec::Vec<u64> {
+        ::std::mem::replace(&mut self.unconverted_l0s, ::std::vec::Vec::new())
+    }
 }
 
 impl ::protobuf::Message for Snapshot {
@@ -3328,6 +3397,9 @@ impl ::protobuf::Message for Snapshot {
                     let tmp = is.read_uint64()?;
                     self.columnar_snap_version = tmp;
                 },
+                15 => {
+                    ::protobuf::rt::read_repeated_uint64_into(wire_type, is, &mut self.unconverted_l0s)?;
+                },
                 _ => {
                     ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                 },
@@ -3385,6 +3457,9 @@ impl ::protobuf::Message for Snapshot {
         if self.columnar_snap_version != 0 {
             my_size += ::protobuf::rt::value_size(14, self.columnar_snap_version, ::protobuf::wire_format::WireTypeVarint);
         }
+        for value in &self.unconverted_l0s {
+            my_size += ::protobuf::rt::value_size(15, *value, ::protobuf::wire_format::WireTypeVarint);
+        };
         my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
         self.cached_size.set(my_size);
         my_size
@@ -3442,6 +3517,9 @@ impl ::protobuf::Message for Snapshot {
         if self.columnar_snap_version != 0 {
             os.write_uint64(14, self.columnar_snap_version)?;
         }
+        for v in &self.unconverted_l0s {
+            os.write_uint64(15, *v)?;
+        };
         os.write_unknown_fields(self.get_unknown_fields())?;
         ::std::result::Result::Ok(())
     }
@@ -3549,6 +3627,11 @@ impl ::protobuf::Message for Snapshot {
                     |m: &Snapshot| { &m.columnar_snap_version },
                     |m: &mut Snapshot| { &mut m.columnar_snap_version },
                 ));
+                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
+                    "unconverted_l0s",
+                    |m: &Snapshot| { &m.unconverted_l0s },
+                    |m: &mut Snapshot| { &mut m.unconverted_l0s },
+                ));
                 ::protobuf::reflect::MessageDescriptor::new::<Snapshot>(
                     "Snapshot",
                     fields,
@@ -3584,6 +3667,7 @@ impl ::protobuf::Clear for Snapshot {
         self.columnar_creates.clear();
         self.schema_meta.clear();
         self.columnar_snap_version = 0;
+        self.unconverted_l0s.clear();
         self.unknown_fields.clear();
     }
 }
@@ -3606,6 +3690,7 @@ impl ::protobuf::PbPrint for Snapshot {
         ::protobuf::PbPrint::fmt(&self.columnar_creates, "columnar_creates", buf);
         ::protobuf::PbPrint::fmt(&self.schema_meta, "schema_meta", buf);
         ::protobuf::PbPrint::fmt(&self.columnar_snap_version, "columnar_snap_version", buf);
+        ::protobuf::PbPrint::fmt(&self.unconverted_l0s, "unconverted_l0s", buf);
         if old_len < buf.len() {
           buf.push(' ');
         }
@@ -3629,6 +3714,7 @@ impl ::std::fmt::Debug for Snapshot {
         ::protobuf::PbPrint::fmt(&self.columnar_creates, "columnar_creates", &mut s);
         ::protobuf::PbPrint::fmt(&self.schema_meta, "schema_meta", &mut s);
         ::protobuf::PbPrint::fmt(&self.columnar_snap_version, "columnar_snap_version", &mut s);
+        ::protobuf::PbPrint::fmt(&self.unconverted_l0s, "unconverted_l0s", &mut s);
         write!(f, "{}", s)
     }
 }
@@ -6617,53 +6703,54 @@ static file_descriptor_proto_data: &'static [u8] = b"\
     \x20\x03(\x04B\0\x12\x14\n\nconflicted\x18\x04\x20\x01(\x08B\0:\0\"K\n\n\
     SchemaMeta\x12\x15\n\x0bkeyspace_id\x18\x01\x20\x01(\rB\0\x12\x11\n\x07f\
     ile_id\x18\x02\x20\x01(\x04B\0\x12\x11\n\x07version\x18\x03\x20\x01(\x03\
-    B\0:\0\"`\n\x12ColumnarCompaction\x120\n\x0fcolumnar_change\x18\x01\x20\
+    B\0:\0\"s\n\x12ColumnarCompaction\x120\n\x0fcolumnar_change\x18\x01\x20\
     \x01(\x0b2\x15.enginepb.TableChangeB\0\x12\x16\n\x0csnap_version\x18\x02\
-    \x20\x01(\x04B\0:\0\"\xab\x01\n\x05Flush\x12&\n\x08l0Create\x18\x01\x20\
-    \x01(\x0b2\x12.enginepb.L0CreateB\0\x12*\n\nproperties\x18\x02\x20\x01(\
-    \x0b2\x14.enginepb.PropertiesB\0\x12\x11\n\x07version\x18\x03\x20\x01(\
-    \x04B\0\x12\x10\n\x06max_ts\x18\x05\x20\x01(\x04B\0\x12'\n\tl0Creates\
-    \x18\x06\x20\x03(\x0b2\x12.enginepb.L0CreateB\0:\0\"\xc4\x03\n\x08Snapsh\
-    ot\x12\x15\n\x0bouter_start\x18\x01\x20\x01(\x0cB\0\x12\x13\n\touter_end\
-    \x18\x02\x20\x01(\x0cB\0\x12*\n\nproperties\x18\x03\x20\x01(\x0b2\x14.en\
-    ginepb.PropertiesB\0\x12'\n\tl0Creates\x18\x05\x20\x03(\x0b2\x12.enginep\
-    b.L0CreateB\0\x12-\n\x0ctableCreates\x18\x06\x20\x03(\x0b2\x15.enginepb.\
-    TableCreateB\0\x12\x15\n\x0bbaseVersion\x18\x07\x20\x01(\x04B\0\x12\x17\
-    \n\rdata_sequence\x18\x08\x20\x01(\x04B\0\x12+\n\x0bBlobCreates\x18\t\
-    \x20\x03(\x0b2\x14.enginepb.BlobCreateB\0\x12\x10\n\x06max_ts\x18\n\x20\
-    \x01(\x04B\0\x12\x17\n\rinner_key_off\x18\x0b\x20\x01(\rB\0\x120\n\x0fco\
-    lumnarCreates\x18\x0c\x20\x03(\x0b2\x15.enginepb.TableCreateB\0\x12+\n\
-    \x0bschema_meta\x18\r\x20\x01(\x0b2\x14.enginepb.SchemaMetaB\0\x12\x1f\n\
-    \x15columnar_snap_version\x18\x0e\x20\x01(\x04B\0:\0\"A\n\x08L0Create\
-    \x12\x0c\n\x02ID\x18\x01\x20\x01(\x04B\0\x12\x12\n\x08smallest\x18\x02\
-    \x20\x01(\x0cB\0\x12\x11\n\x07biggest\x18\x03\x20\x01(\x0cB\0:\0\"C\n\nB\
-    lobCreate\x12\x0c\n\x02ID\x18\x01\x20\x01(\x04B\0\x12\x12\n\x08smallest\
-    \x18\x02\x20\x01(\x0cB\0\x12\x11\n\x07biggest\x18\x03\x20\x01(\x0cB\0:\0\
-    \"~\n\x0bTableCreate\x12\x0c\n\x02ID\x18\x01\x20\x01(\x04B\0\x12\x0f\n\
-    \x05level\x18\x02\x20\x01(\rB\0\x12\x0c\n\x02CF\x18\x03\x20\x01(\x05B\0\
-    \x12\x12\n\x08smallest\x18\x04\x20\x01(\x0cB\0\x12\x11\n\x07biggest\x18\
-    \x05\x20\x01(\x0cB\0\x12\x19\n\x0fcolumnar_tables\x18\x06\x20\x01(\rB\0:\
-    \0\"<\n\x0bTableDelete\x12\x0c\n\x02ID\x18\x01\x20\x01(\x04B\0\x12\x0f\n\
-    \x05level\x18\x02\x20\x01(\rB\0\x12\x0c\n\x02CF\x18\x03\x20\x01(\x05B\0:\
-    \0\"D\n\x05Split\x12)\n\tnewShards\x18\x01\x20\x03(\x0b2\x14.enginepb.Pr\
-    opertiesB\0\x12\x0e\n\x04Keys\x18\x03\x20\x03(\x0cB\0:\0\"\xd2\x01\n\x0b\
-    IngestFiles\x12'\n\tl0Creates\x18\x01\x20\x03(\x0b2\x12.enginepb.L0Creat\
-    eB\0\x12-\n\x0ctableCreates\x18\x02\x20\x03(\x0b2\x15.enginepb.TableCrea\
-    teB\0\x12*\n\nproperties\x18\x03\x20\x01(\x0b2\x14.enginepb.PropertiesB\
-    \0\x12+\n\x0bBlobCreates\x18\x04\x20\x03(\x0b2\x14.enginepb.BlobCreateB\
-    \0\x12\x10\n\x06max_ts\x18\x05\x20\x01(\x04B\0:\0\"C\n\nProperties\x12\
-    \x11\n\x07shardID\x18\x01\x20\x01(\x04B\0\x12\x0e\n\x04keys\x18\x02\x20\
-    \x03(\tB\0\x12\x10\n\x06values\x18\x03\x20\x03(\x0cB\0:\0\"m\n\x0bTableC\
-    hange\x12-\n\x0ctableDeletes\x18\x01\x20\x03(\x0b2\x15.enginepb.TableDel\
-    eteB\0\x12-\n\x0ctableCreates\x18\x02\x20\x03(\x0b2\x15.enginepb.TableCr\
-    eateB\0:\0\">\n\x0bTxnFileRefs\x12-\n\rtxn_file_refs\x18\x01\x20\x03(\
-    \x0b2\x14.enginepb.TxnFileRefB\0:\0\"\xc9\x01\n\nTxnFileRef\x12\x12\n\
-    \x08start_ts\x18\x01\x20\x01(\x04B\0\x12\x13\n\tchunk_ids\x18\x02\x20\
-    \x03(\x04B\0\x12\x11\n\x07version\x18\x03\x20\x01(\x04B\0\x12\x13\n\tuse\
-    r_meta\x18\x04\x20\x01(\x0cB\0\x12\x19\n\x0flock_val_prefix\x18\x05\x20\
-    \x01(\x0cB\0\x12\x13\n\tshard_ver\x18\x06\x20\x01(\x04B\0\x12\x1b\n\x11i\
-    nner_lower_bound\x18\x07\x20\x01(\x0cB\0\x12\x1b\n\x11inner_upper_bound\
-    \x18\x08\x20\x01(\x0cB\0:\0B\0b\x06proto3\
+    \x20\x01(\x04B\0\x12\x11\n\x07row_l0s\x18\x03\x20\x03(\x04B\0:\0\"\xab\
+    \x01\n\x05Flush\x12&\n\x08l0Create\x18\x01\x20\x01(\x0b2\x12.enginepb.L0\
+    CreateB\0\x12*\n\nproperties\x18\x02\x20\x01(\x0b2\x14.enginepb.Properti\
+    esB\0\x12\x11\n\x07version\x18\x03\x20\x01(\x04B\0\x12\x10\n\x06max_ts\
+    \x18\x05\x20\x01(\x04B\0\x12'\n\tl0Creates\x18\x06\x20\x03(\x0b2\x12.eng\
+    inepb.L0CreateB\0:\0\"\xdf\x03\n\x08Snapshot\x12\x15\n\x0bouter_start\
+    \x18\x01\x20\x01(\x0cB\0\x12\x13\n\touter_end\x18\x02\x20\x01(\x0cB\0\
+    \x12*\n\nproperties\x18\x03\x20\x01(\x0b2\x14.enginepb.PropertiesB\0\x12\
+    '\n\tl0Creates\x18\x05\x20\x03(\x0b2\x12.enginepb.L0CreateB\0\x12-\n\x0c\
+    tableCreates\x18\x06\x20\x03(\x0b2\x15.enginepb.TableCreateB\0\x12\x15\n\
+    \x0bbaseVersion\x18\x07\x20\x01(\x04B\0\x12\x17\n\rdata_sequence\x18\x08\
+    \x20\x01(\x04B\0\x12+\n\x0bBlobCreates\x18\t\x20\x03(\x0b2\x14.enginepb.\
+    BlobCreateB\0\x12\x10\n\x06max_ts\x18\n\x20\x01(\x04B\0\x12\x17\n\rinner\
+    _key_off\x18\x0b\x20\x01(\rB\0\x120\n\x0fcolumnarCreates\x18\x0c\x20\x03\
+    (\x0b2\x15.enginepb.TableCreateB\0\x12+\n\x0bschema_meta\x18\r\x20\x01(\
+    \x0b2\x14.enginepb.SchemaMetaB\0\x12\x1f\n\x15columnar_snap_version\x18\
+    \x0e\x20\x01(\x04B\0\x12\x19\n\x0funconverted_l0s\x18\x0f\x20\x03(\x04B\
+    \0:\0\"A\n\x08L0Create\x12\x0c\n\x02ID\x18\x01\x20\x01(\x04B\0\x12\x12\n\
+    \x08smallest\x18\x02\x20\x01(\x0cB\0\x12\x11\n\x07biggest\x18\x03\x20\
+    \x01(\x0cB\0:\0\"C\n\nBlobCreate\x12\x0c\n\x02ID\x18\x01\x20\x01(\x04B\0\
+    \x12\x12\n\x08smallest\x18\x02\x20\x01(\x0cB\0\x12\x11\n\x07biggest\x18\
+    \x03\x20\x01(\x0cB\0:\0\"~\n\x0bTableCreate\x12\x0c\n\x02ID\x18\x01\x20\
+    \x01(\x04B\0\x12\x0f\n\x05level\x18\x02\x20\x01(\rB\0\x12\x0c\n\x02CF\
+    \x18\x03\x20\x01(\x05B\0\x12\x12\n\x08smallest\x18\x04\x20\x01(\x0cB\0\
+    \x12\x11\n\x07biggest\x18\x05\x20\x01(\x0cB\0\x12\x19\n\x0fcolumnar_tabl\
+    es\x18\x06\x20\x01(\rB\0:\0\"<\n\x0bTableDelete\x12\x0c\n\x02ID\x18\x01\
+    \x20\x01(\x04B\0\x12\x0f\n\x05level\x18\x02\x20\x01(\rB\0\x12\x0c\n\x02C\
+    F\x18\x03\x20\x01(\x05B\0:\0\"D\n\x05Split\x12)\n\tnewShards\x18\x01\x20\
+    \x03(\x0b2\x14.enginepb.PropertiesB\0\x12\x0e\n\x04Keys\x18\x03\x20\x03(\
+    \x0cB\0:\0\"\xd2\x01\n\x0bIngestFiles\x12'\n\tl0Creates\x18\x01\x20\x03(\
+    \x0b2\x12.enginepb.L0CreateB\0\x12-\n\x0ctableCreates\x18\x02\x20\x03(\
+    \x0b2\x15.enginepb.TableCreateB\0\x12*\n\nproperties\x18\x03\x20\x01(\
+    \x0b2\x14.enginepb.PropertiesB\0\x12+\n\x0bBlobCreates\x18\x04\x20\x03(\
+    \x0b2\x14.enginepb.BlobCreateB\0\x12\x10\n\x06max_ts\x18\x05\x20\x01(\
+    \x04B\0:\0\"C\n\nProperties\x12\x11\n\x07shardID\x18\x01\x20\x01(\x04B\0\
+    \x12\x0e\n\x04keys\x18\x02\x20\x03(\tB\0\x12\x10\n\x06values\x18\x03\x20\
+    \x03(\x0cB\0:\0\"m\n\x0bTableChange\x12-\n\x0ctableDeletes\x18\x01\x20\
+    \x03(\x0b2\x15.enginepb.TableDeleteB\0\x12-\n\x0ctableCreates\x18\x02\
+    \x20\x03(\x0b2\x15.enginepb.TableCreateB\0:\0\">\n\x0bTxnFileRefs\x12-\n\
+    \rtxn_file_refs\x18\x01\x20\x03(\x0b2\x14.enginepb.TxnFileRefB\0:\0\"\
+    \xc9\x01\n\nTxnFileRef\x12\x12\n\x08start_ts\x18\x01\x20\x01(\x04B\0\x12\
+    \x13\n\tchunk_ids\x18\x02\x20\x03(\x04B\0\x12\x11\n\x07version\x18\x03\
+    \x20\x01(\x04B\0\x12\x13\n\tuser_meta\x18\x04\x20\x01(\x0cB\0\x12\x19\n\
+    \x0flock_val_prefix\x18\x05\x20\x01(\x0cB\0\x12\x13\n\tshard_ver\x18\x06\
+    \x20\x01(\x04B\0\x12\x1b\n\x11inner_lower_bound\x18\x07\x20\x01(\x0cB\0\
+    \x12\x1b\n\x11inner_upper_bound\x18\x08\x20\x01(\x0cB\0:\0B\0b\x06proto3\
 ";
 
 static mut file_descriptor_proto_lazy: ::protobuf::lazy::Lazy<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::lazy::Lazy {
diff --git a/components/native_br/src/restore_keyspace.rs b/components/native_br/src/restore_keyspace.rs
index b7855d214..5e40ee1ea 100644
--- a/components/native_br/src/restore_keyspace.rs
+++ b/components/native_br/src/restore_keyspace.rs
@@ -1760,13 +1760,7 @@ impl BackupCluster {
                 let shard = self.get_shard(shard_id).unwrap();
                 for (&file_id, file_meta) in shard.meta.all_files() {
                     if meta.overlap_table(file_meta.smallest(), file_meta.biggest()) {
-                        meta.add_file(
-                            file_id,
-                            file_meta.cf as i32,
-                            file_meta.level as u32,
-                            &file_meta.smallest,
-                            &file_meta.biggest,
-                        );
+                        meta.add_file(file_id, file_meta.clone());
                         sstables_cnt += 1;
                     }
                 }
diff --git a/components/rfstore/src/store/msg.rs b/components/rfstore/src/store/msg.rs
index 114dd55f7..39da02119 100644
--- a/components/rfstore/src/store/msg.rs
+++ b/components/rfstore/src/store/msg.rs
@@ -3,7 +3,7 @@
 use std::{borrow::Cow, collections::VecDeque, fmt, fmt::Debug, sync::Arc};
 
 use cloud_encryption::EncryptionKey;
-use kvengine::table::columnar::schema_file::SchemaFile;
+use kvengine::table::columnar::SchemaFile;
 use kvenginepb::TxnFileRef;
 use kvproto::{
     kvrpcpb::ExtraOp as TxnExtraOp,
diff --git a/components/rfstore/src/store/peer_fsm.rs b/components/rfstore/src/store/peer_fsm.rs
index 3678f1714..6151f6afb 100644
--- a/components/rfstore/src/store/peer_fsm.rs
+++ b/components/rfstore/src/store/peer_fsm.rs
@@ -16,9 +16,9 @@ use bytes::Buf;
 use error_code::ErrorCodeExt;
 use fail::fail_point;
 use kvengine::{
-    table::columnar::schema_file::SchemaFile, CheckMergeResult, IdVer, Shard, TruncateTs,
-    DEL_PREFIXES_KEY, MANUAL_MAJOR_COMPACTION, MANUAL_MAJOR_COMPACTION_DISABLE,
-    MANUAL_MAJOR_COMPACTION_ENABLE, TRUNCATE_TS_KEY,
+    table::columnar::SchemaFile, CheckMergeResult, IdVer, Shard, TruncateTs, DEL_PREFIXES_KEY,
+    MANUAL_MAJOR_COMPACTION, MANUAL_MAJOR_COMPACTION_DISABLE, MANUAL_MAJOR_COMPACTION_ENABLE,
+    TRUNCATE_TS_KEY,
 };
 use kvproto::{
     import_sstpb::SwitchMode,
diff --git a/components/tidb_query_datatype/src/codec/row/v2/row_slice.rs b/components/tidb_query_datatype/src/codec/row/v2/row_slice.rs
index d419b5949..159e2f917 100644
--- a/components/tidb_query_datatype/src/codec/row/v2/row_slice.rs
+++ b/components/tidb_query_datatype/src/codec/row/v2/row_slice.rs
@@ -174,8 +174,16 @@ impl RowSlice<'_> {
                 null_ids,
                 ..
             } => {
-                let max_non_null_id = non_null_ids.get(non_null_ids.len() - 1).unwrap_or(0);
-                let max_null_id = null_ids.get(null_ids.len() - 1).unwrap_or(0);
+                let max_non_null_id = if non_null_ids.len() > 0 {
+                    non_null_ids.get(non_null_ids.len() - 1).unwrap()
+                } else {
+                    0
+                };
+                let max_null_id = if null_ids.len() > 0 {
+                    null_ids.get(null_ids.len() - 1).unwrap()
+                } else {
+                    0
+                };
                 max_non_null_id.max(max_null_id) as i32
             }
             RowSlice::Small {
@@ -183,8 +191,16 @@ impl RowSlice<'_> {
                 null_ids,
                 ..
             } => {
-                let max_non_null_id = non_null_ids.get(non_null_ids.len() - 1).unwrap_or(0);
-                let max_null_id = null_ids.get(null_ids.len() - 1).unwrap_or(0);
+                let max_non_null_id = if non_null_ids.len() > 0 {
+                    non_null_ids.get(non_null_ids.len() - 1).unwrap()
+                } else {
+                    0
+                };
+                let max_null_id = if null_ids.len() > 0 {
+                    null_ids.get(null_ids.len() - 1).unwrap()
+                } else {
+                    0
+                };
                 max_non_null_id.max(max_null_id) as i32
             }
         }
diff --git a/tests/cloud_engine/columnar/mod.rs b/tests/cloud_engine/columnar/mod.rs
index d6ee33bf5..b0163d01b 100644
--- a/tests/cloud_engine/columnar/mod.rs
+++ b/tests/cloud_engine/columnar/mod.rs
@@ -1,21 +1,31 @@
 // Copyright 2024 TiKV Project Authors. Licensed under Apache-2.0.
 
-use std::time::Duration;
+use std::{sync::Mutex, time::Duration};
 
 use api_version::ApiV2;
+use bytes::Buf;
 use hyper::Body;
 use kvengine::{
     dfs,
     dfs::FileType,
-    table::columnar::{
-        builder::{new_int_handle_column_info, new_txn_id_column_info, new_version_column_info},
-        columnar::Schema,
-        schema_file::build_schema_file,
+    table::{
+        columnar,
+        columnar::{
+            build_schema_file, new_int_handle_column_info, new_txn_id_column_info,
+            new_version_column_info, Schema,
+        },
     },
 };
 use pd_client::PdClient;
 use test_cloud_server::{must_wait, ServerCluster};
-use tidb_query_datatype::{codec::table::TABLE_PREFIX, Collation, FieldTypeTp};
+use tidb_query_datatype::{
+    codec::{
+        row::v2::encoder_for_test::{Column, RowEncoder},
+        table::{encode_row_key, TABLE_PREFIX},
+    },
+    expr::EvalContext,
+    Collation, FieldTypeTp,
+};
 use tikv_util::codec::{bytes::encode_bytes, number::NumberEncoder};
 use tipb::ColumnInfo;
 use txn_types::Key;
@@ -122,6 +132,135 @@ fn test_schema_file() {
     }
 }
 
+#[test]
+fn test_covert_row_to_columnar() {
+    test_util::init_log_for_test();
+    let node_id = alloc_node_id();
+    let mut cluster = ServerCluster::new(vec![node_id], |_, conf| {
+        conf.enable_inner_key_offset = true;
+        conf.kvengine
+            .columnar_table_build_options
+            .max_columnar_table_size = 1024;
+        conf.kvengine
+            .columnar_table_build_options
+            .pack_max_row_count = 9;
+    });
+    let dfs = cluster.get_dfs().unwrap();
+    let keyspace_id = 7;
+    let table_ids = dfs
+        .get_runtime()
+        .block_on(create_keyspace_and_split_tables(&mut cluster, keyspace_id));
+    let table_id = table_ids[1];
+    let schemas = build_schemas(vec![table_id]);
+    let mut schema = schemas[0].clone();
+    schema.txn_id_column = None;
+    let schema_version = 10;
+    let schema_file_data = build_schema_file(keyspace_id, schema_version, schemas);
+    let schema_file_id = 100;
+    let opts = dfs::Options::default().with_type(FileType::Schema);
+    dfs.get_runtime()
+        .block_on(dfs.create(schema_file_id, schema_file_data.into(), opts))
+        .unwrap();
+    let status_addr = cluster.status_addr(node_id);
+    dfs.get_runtime().block_on(send_schema_file_request(
+        &status_addr,
+        keyspace_id,
+        schema_file_id,
+    ));
+    let kvengine = cluster.get_kvengine(node_id);
+    must_wait(
+        || {
+            let all_id_vers = kvengine.get_all_shard_id_vers();
+            for id_ver in all_id_vers {
+                if let Ok(shard) = kvengine.get_shard_with_ver(id_ver.id, id_ver.ver) {
+                    if shard.get_schema_file().is_some() {
+                        return true;
+                    }
+                }
+            }
+            false
+        },
+        10,
+        || "failed to build schema file".to_string(),
+    );
+    let mut client = cluster.new_client();
+    let ctx = Mutex::new(EvalContext::default());
+    client.put_kv(
+        0..100,
+        |i: usize| gen_row_key(keyspace_id, table_id, i),
+        |i: usize| gen_row_val(&ctx, i),
+    );
+    client.put_kv(
+        100..200,
+        |i: usize| gen_row_key(keyspace_id, table_id, i),
+        |i: usize| gen_row_val(&ctx, i),
+    );
+    let mut shard_id = None;
+    must_wait(
+        || {
+            let all_id_vers = kvengine.get_all_shard_id_vers();
+            for id_ver in all_id_vers {
+                if let Ok(shard) = kvengine.get_shard_with_ver(id_ver.id, id_ver.ver) {
+                    let snap_version = shard.get_snap_version();
+                    let columnar_snap_version = shard.get_columnar_snap_version();
+                    if snap_version == columnar_snap_version && columnar_snap_version >= 29 {
+                        shard_id = Some(id_ver.id);
+                        return true;
+                    }
+                }
+            }
+            false
+        },
+        10,
+        || "failed to build columnar file".to_string(),
+    );
+    let shard_id = shard_id.unwrap();
+    let shard = kvengine.get_shard(shard_id).unwrap();
+    let snap_access = shard.new_snap_access();
+    let ts = client.get_ts().into_inner();
+    let mut columnar_reader = snap_access.new_columnar_mvcc_reader(&schema, ts).unwrap();
+    let start_handle = 0i64.to_le_bytes().to_vec();
+    let end_handle = 190i64.to_le_bytes().to_vec();
+    columnar_reader
+        .set_handle_range(&start_handle, &end_handle)
+        .unwrap();
+    let mut block = columnar::Block::new(&schema);
+    let read_rows = columnar_reader.read_block(&mut block, 200).unwrap();
+    assert_eq!(read_rows, 190);
+    for i in 0..read_rows {
+        let handle = block.get_handle_buf().get_int_handle_value(i);
+        assert_eq!(handle, i as i64);
+        let columns = block.get_columns();
+        assert_eq!(columns[0].get_not_null_value(i).get_i64_le(), i as i64);
+        let str_val = gen_str_val(i);
+        assert_eq!(columns[1].get_not_null_value(i), &str_val);
+    }
+}
+
+fn gen_row_key(keyspace_id: u32, table_id: i64, i: usize) -> Vec<u8> {
+    let mut key = ApiV2::get_txn_keyspace_prefix(keyspace_id);
+    let table_key = encode_row_key(table_id, i as i64);
+    key.extend_from_slice(&table_key);
+    key
+}
+
+fn gen_row_val(ctx: &Mutex<EvalContext>, i: usize) -> Vec<u8> {
+    let mut row_val = vec![];
+    let str_val = gen_str_val(i);
+    let cols = vec![
+        Column::new(1, Some(i as i64)),
+        Column::new(2, Some(str_val)),
+    ];
+    let mut guard = ctx.lock().unwrap();
+    row_val.write_row(&mut guard, cols).unwrap();
+    row_val
+}
+
+fn gen_str_val(i: usize) -> Vec<u8> {
+    let repeat = 1 + i % 16;
+    format!("abc_{}", i).repeat(repeat).into_bytes()
+}
+
 fn build_schemas(table_ids: Vec<i64>) -> Vec<Schema> {
     let mut schemas = vec![];
     for &columnar_table_id in &table_ids {
